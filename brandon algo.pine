//@version=6
indicator('[Brandon<PERSON><PERSON><PERSON>] Algo Beta V4.8.2', overlay=true)

//────────────────────────────────────────────────────────────
// CONSTANTS & GLOBAL VARIABLES
//────────────────────────────────────────────────────────────
const int DECIMAL_PLACES    = 2
const int MAX_ROWS          = 15
const int TITLE_ROW         = 0    // Title row of the dashboard
const int HEADER_ROW        = 1    // Header row (column labels)

const string DASHBOARD_TITLE = "[BrandonJames] Algo Beta V4.8.2 Dashboard"

//────────────────────────────────────────────────────────────
// INPUTS
//────────────────────────────────────────────────────────────
// Groups for input organization
var string g_trend         = 'Trend Detection'
var string g_perfectTrail  = 'Perfect Trail Settings'
var string g_macd          = 'MACD Settings'
var string g_rsi           = 'RSI Settings'
var string g_dmi           = 'DMI Settings'
var string g_weights       = 'Indicator Weights'
var string g_dashboard     = 'Dashboard Settings'
var string g_alerts        = 'Alert Settings'
var string g_nwe           = 'Nadaraya-Watson Envelope Settings'
var string g_mtf           = 'Multi-Timeframe Analysis'
var string g_volume        = 'Volume Analysis'
var string g_swing         = 'Swing Point Analysis'
var string g_sentiment     = 'Sentiment Analysis Settings'
var string g_ml            = 'Advanced Adaptive Learning Settings'
var string g_rl            = 'Heuristic Parameter Optimization Settings'

// Trend Detection Inputs
useMacd = input.bool(true, 'Use MACD', group=g_trend)
useRsi  = input.bool(true, 'Use RSI', group=g_trend)
useDmi  = input.bool(true, 'Use DMI', group=g_trend)
trendPeriod = input.int(21, 'Trend Period', group=g_trend)
adxPeriod = input.int(18, 'ADX Period for Trend Detection', minval=1, group=g_trend)
adxThresholdTrending = input.int(22, 'ADX Threshold for Trending', minval=1, maxval=100, group=g_trend)
volatilityImpact = input.float(0.5, 'Volatility Impact on Threshold', minval=0.0, maxval=1.0, step=0.1, group=g_trend)
volMAPeriod = input.int(10, 'Volatility MA Period', group=g_trend)
volatilityLookback = input.int(75, 'Volatility Lookback Period', minval=10, maxval=500, step=5, group=g_trend)
debugMode = input.bool(false, 'Debug Mode', group=g_trend)

// Perfect Trail Settings
showPerfectTrail = input.bool(true, 'Show Perfect Trail', group=g_perfectTrail)
showTrailSignals = input.bool(false, 'Show Trail Signals', group=g_perfectTrail)
perfectTrailMAType = input.string('ema', 'MA type', ['sma', 'ema', 'wma', 'rma', 'vwma'], group=g_perfectTrail)
perfectTrailMALength = input.int(70, 'MA Length', minval=20, group=g_perfectTrail)
perfectTrailATRLength = input.int(200, 'ATR Length', minval=10, group=g_perfectTrail)
perfectTrailBandsDist = input.float(3, 'Bands Distance', step=0.1, minval=2, group=g_perfectTrail)
perfectTrailConfirmBars = input.int(1, 'Confirmation Bars', minval=0, maxval=5, group=g_perfectTrail)
trailSignalConfirmation = input.int(2, 'Signal Confirmation Bars', minval=1, maxval=5, group=g_perfectTrail)
perfectTrailBullColor = input.color(#1fa075, 'Bull Color', group=g_perfectTrail)
perfectTrailBearColor = input.color(#ad2530, 'Bear Color', group=g_perfectTrail)
perfectTrailBandOpacity = input.float(95, 'Band Opacity', minval=0, maxval=100, group=g_perfectTrail)
perfectTrailBand2Mult = input.float(1.5, 'Band 2 Multiplier', step=0.1, group=g_perfectTrail)
perfectTrailBand3Mult = input.float(3.0, 'Band 3 Multiplier', step=0.1, group=g_perfectTrail)



// MACD Inputs
macdFastLength    = input.int(12, 'MACD Fast Length', group=g_macd)
macdSlowLength    = input.int(26, 'MACD Slow Length', group=g_macd)
macdSignalLength  = input.int(9, 'MACD Signal Length', group=g_macd)

// RSI Inputs
rsiLength   = input.int(14, 'RSI Length', group=g_rsi)
rsiPeriod1  = input.int(14, 'RSI Period 1', minval=1, group=g_rsi)
rsiPeriod2  = input.int(17, 'RSI Period 2', minval=1, group=g_rsi)
stochLength = input.int(14, 'Stochastic Length', minval=1, group=g_rsi)
smoothK     = input.int(3, 'Smooth K', minval=1, group=g_rsi)
smoothD     = input.int(3, 'Smooth D', minval=1, group=g_rsi)

// DMI Inputs
dmiLength    = input.int(14, 'DMI Length', group=g_dmi)
adxThreshold = input.int(25, 'ADX Threshold', group=g_dmi)

// Dashboard Options
showDashboard     = input.bool(true, 'Show Dashboard', group=g_dashboard)
dashboardMode     = input.string('Standard', 'Dashboard Mode', options=['Compact', 'Standard', 'Detailed'], group=g_dashboard, tooltip="Compact: minimal info, Standard: balanced view, Detailed: all information")
dashboardPosition = input.string('Bottom Right', 'Dashboard Position', options=['Top Left', 'Top Right', 'Bottom Left', 'Bottom Right'], group=g_dashboard)
showTrendMeter    = input.bool(true, 'Show Trend Strength Meter', group=g_dashboard)
showCurrentTF     = input.bool(true, 'Show Current Timeframe', group=g_dashboard)
dashboardColorTheme = input.string('Default', 'Color Theme', options=['Default', 'Monochrome', 'High Contrast', 'Pastel'], group=g_dashboard)
dashboardTextSize = input.string('Small', 'Dashboard Text Size', options=['Tiny', 'Small', 'Normal', 'Large'], group=g_dashboard)
dashboardOpacity  = input.int(20, 'Dashboard Opacity', minval=0, maxval=100, group=g_dashboard)
showTooltips      = input.bool(true, 'Show Tooltips on Hover', group=g_dashboard, tooltip="Shows additional information when hovering over dashboard elements")

// Indicator Weights
useDynamicWeights = input.bool(true, 'Use Dynamic Weights', group=g_weights)
macdWeight = input.float(1.0, 'MACD Weight', minval=0.0, maxval=5.0, step=0.1, group=g_weights)
rsiWeight  = input.float(1.0, 'RSI Weight', minval=0.0, maxval=5.0, step=0.1, group=g_weights)
dmiWeight  = input.float(1.0, 'DMI Weight', minval=0.0, maxval=5.0, step=0.1, group=g_weights)
dynamicWeightStrength = input.float(0.45, 'Dynamic Weight Strength', minval=0.0, maxval=1.0, step=0.1, group=g_weights)


// Machine Learning Settings (Advanced Adaptive Algorithm)
// Note: This implements an advanced adaptive algorithm that emulates ML concepts.
// It uses deterministic calculations for backtesting consistency.
useML = input.bool(true, 'Use Advanced Adaptive Learning', group=g_ml, tooltip="Enables advanced adaptive algorithm that optimizes indicator weights based on historical performance. Uses deterministic calculations for consistent backtesting.")
mlWeightInfluence = input.float(0.7, 'Adaptive Weight Influence', minval=0.0, maxval=1.0, step=0.1, group=g_ml, tooltip="Controls how much the adaptive learning affects indicator weights")
showMLInfo = input.bool(true, 'Show Adaptive Info in Dashboard', group=g_ml)
mlLookback = input.int(50, 'Learning Lookback Period', minval=20, maxval=500, step=10, group=g_ml)
mlUpdateFrequency = input.int(8, 'Update Frequency (bars)', minval=1, maxval=50, group=g_ml)
mlLearningRate = input.float(0.03, 'Learning Rate', minval=0.01, maxval=0.5, step=0.01, group=g_ml)

// Reinforcement Learning Settings (Heuristic-Based Parameter Optimization)
// Note: This implements a heuristic-based system that emulates RL concepts.
// Uses pseudo-random exploration for deterministic backtesting behavior.
useRL = input.bool(true, 'Use Heuristic Parameter Optimization', group=g_rl, tooltip="Enables heuristic-based parameter optimization system. Uses deterministic pseudo-random exploration for consistent backtesting results.")
showRLInfo = input.bool(true, 'Show Optimization Info in Dashboard', group=g_rl)
rlHarmonizationStrength = input.float(0.65, 'Parameter Harmonization', minval=0.0, maxval=1.0, step=0.1, group=g_rl, tooltip="Controls how strongly parameters are harmonized to work together")
rlExplorationRate = input.float(0.1, 'Exploration Rate', minval=0.0, maxval=0.5, step=0.05, group=g_rl)
rlDecayRate = input.float(0.995, 'Exploration Decay Rate', minval=0.9, maxval=0.999, step=0.001, group=g_rl)
rlLearningRate = input.float(0.008, 'RL Learning Rate', minval=0.001, maxval=0.1, step=0.001, group=g_rl)
rlUpdateFrequency = input.int(15, 'RL Update Frequency (bars)', minval=1, maxval=100, step=1, group=g_rl)
rlRewardWindow = input.int(25, 'Reward Calculation Window', minval=5, maxval=100, step=5, group=g_rl)

// Nadaraya-Watson Envelope Inputs
src  = input(close, 'Source', group=g_nwe)
h    = input.float(8.0, 'Bandwidth', minval=0, group=g_nwe)
mult = input.float(3.0, 'Multiplier', minval=0, group=g_nwe)

// Alert Settings
alertOnClose            = input.bool(true, 'Trigger Alerts on Bar Close', group=g_alerts)
alertStrongReversal     = input.bool(true, 'Strong Reversal (Yellow)', group=g_alerts)
alertVeryStrongReversal = input.bool(true, 'Very Strong Reversal (Blue)', group=g_alerts)
alertExtremeReversal    = input.bool(true, 'Extreme Reversal (Purple)', group=g_alerts)
alertTrendStart         = input.bool(false, 'Trend Start', group=g_alerts)
alertSignificantChange  = input.bool(false, 'Significant Trend Change', group=g_alerts)

// Multi-Timeframe Analysis Settings
mtfEnabled = input.bool(true, 'Enable Multi-Timeframe Analysis', group=g_mtf)
higherTF = input.string('240', 'Higher Timeframe', options=['60', '120', '240', 'D'], group=g_mtf)
lowerTF = input.string('5', 'Lower Timeframe', options=['1', '3', '5', '15'], group=g_mtf)
mtfWeight = input.float(0.35, 'Higher Timeframe Weight', minval=0.0, maxval=1.0, step=0.1, group=g_mtf)
mtfAlignmentThreshold = input.float(70, 'Alignment Threshold', minval=50, maxval=90, step=5, group=g_mtf, tooltip="Minimum alignment score between timeframes to consider the trend valid")

// Volume Analysis Settings
useVolumeAnalysis = input.bool(true, 'Use Volume Analysis', group=g_volume)
volumeImpact = input.float(0.25, 'Volume Impact on Trend Score', minval=0.0, maxval=1.0, step=0.1, group=g_volume)
obvPeriod = input.int(20, 'OBV Smoothing Period', minval=1, group=g_volume)
volumeDivergenceLength = input.int(14, 'Volume Divergence Length', minval=5, maxval=50, group=g_volume)

// Swing Point Analysis Settings
useSwingPoints = input.bool(true, 'Use Swing Point Analysis', group=g_swing)
swingImpact = input.float(0.35, 'Swing Impact on Trend Score', minval=0.0, maxval=1.0, step=0.1, group=g_swing)
swingStrength = input.int(3, 'Swing Point Strength', minval=1, maxval=10, group=g_swing)
swingLookback = input.int(50, 'Swing Point Lookback', minval=10, group=g_swing)

// Sentiment Analysis Settings
useSentimentAnalysis = input.bool(true, 'Use Sentiment Analysis', group=g_sentiment)
sentimentWeight = input.float(0.25, 'Sentiment Impact on Trend Score', minval=0.0, maxval=1.0, step=0.1, group=g_sentiment)
sentimentLookback = input.int(10, 'Sentiment Lookback Periods', minval=5, maxval=50, group=g_sentiment)
sentimentVolatilityFilter = input.bool(true, 'Filter Sentiment in High Volatility', group=g_sentiment)

//────────────────────────────────────────────────────────────
// GAUSSIAN WINDOW CALCULATION (for NWE)
//────────────────────────────────────────────────────────────
gauss(x, h) => math.exp(- (math.pow(x, 2) / (2 * h * h)))

var coefs = array.new_float(0)
var float den = 0.0
window_size = 250  // Adjust window size as needed

// FIX: Calculate the change detection on every bar to ensure consistent calculations.
// This avoids the error of calling a history-dependent function inside a conditional block.
bool h_has_changed = ta.change(h) != 0

// FIX: The condition now uses the pre-calculated boolean variable `h_has_changed`.
// This resolves both the type mismatch error and the inconsistent calculation warning.
if barstate.isfirst or h_has_changed
    array.clear(coefs)
    for i = 0 to window_size - 1 // Loop up to window_size - 1 to avoid index out of bounds
        w = gauss(i, h)
        array.push(coefs, w)
    den := array.sum(coefs)

// Calculate weighted sum more efficiently
var float out = 0.0

// For simplicity and reliability, we'll use the full calculation every time
// This is safer than an incremental approach which can accumulate errors
float temp_out = 0.0
// Ensure the loop does not go out of bounds of the available bars or the coefficients array
int loop_end = math.min(window_size - 1, bar_index, array.size(coefs) - 1)
for i = 0 to loop_end
    temp_out += nz(src[i]) * array.get(coefs, i)

// Avoid division by zero
out := den != 0 ? temp_out / den : nz(src)

mae = ta.sma(math.abs(src - out), window_size) * mult

upper = out + mae
lower = out - mae


// ──────────────────────────────────────────────────────────
// FUNCTIONS: REINFORCEMENT LEARNING
// ──────────────────────────────────────────────────────────

// Calculate reward based on prediction and actual outcome
f_calculateReward(float prediction, float actual) =>
    float reward = 0.0
    if math.sign(prediction) == math.sign(actual)
        reward := 1.0
        float magnitudeError = math.abs(prediction - actual) / math.max(math.abs(actual), 0.001)
        if magnitudeError < 0.5
            reward := reward + (1.0 - magnitudeError)
    else
        reward := -1.0
    reward

// Calculate cumulative reward over the reward window
f_calculateCumulativeReward(float[] rwdHistory) =>
    float cumulativeReward = 0.0
    int historySize = array.size(rwdHistory)
    if historySize > 0
        int windowSize = math.min(rlRewardWindow, historySize)
        for i = 0 to windowSize - 1
            int index = historySize - 1 - i
            if index >= 0
                cumulativeReward := cumulativeReward + array.get(rwdHistory, index)
    cumulativeReward

var float[] gSeed = array.new_float(1, 1.0)

f_pseudoRandom() =>
    float s   = array.get(gSeed, 0)
    float nxt = math.sin(s) * 43758.5453123
    nxt      := nxt - math.floor(nxt)
    array.set(gSeed, 0, nxt)   // write the updated seed back
    nxt

//────────────────────────────────────────────────────────────
// REINFORCEMENT-LEARNING  – ε-greedy action choice (fixed)
//────────────────────────────────────────────────────────────
f_chooseAction(float e, float reward) =>
    // one random draw  →  exact exploration probability = ε
    float r = f_pseudoRandom()
    if r < e                                    // explore
        math.round(f_pseudoRandom()*2)          //   0,1,2 uniform
    else                                        // exploit
        reward >= 0 ? 1 : math.round(f_pseudoRandom())*2

// Apply RL action to adjust a parameter
f_applyRLAction(float currentValue, float minValue, float maxValue, int action) =>
    float newValue = currentValue
    float stepSize = (maxValue - minValue) * rlLearningRate
    if action == 0
        newValue := math.max(minValue, currentValue - stepSize)
    else if action == 2
        newValue := math.min(maxValue, currentValue + stepSize)
    newValue

// Update parameters using RL with simplified parameter harmonization
f_updateRLParameters(float currentReward, float currentExplorationRate,
                     float currentVolatilityImpact,
                     float currentDynamicWeightStrength, float currentMlWeightInfluence) =>
    int volatilityImpactAction = f_chooseAction(currentExplorationRate, currentReward)
    int dynamicWeightStrengthAction = f_chooseAction(currentExplorationRate, currentReward)
    int mlWeightInfluenceAction = f_chooseAction(currentExplorationRate, currentReward)

    float newVolatilityImpact = f_applyRLAction(currentVolatilityImpact, 0.0, 1.0, volatilityImpactAction)
    float newDynamicWeightStrength = f_applyRLAction(currentDynamicWeightStrength, 0.0, 1.0, dynamicWeightStrengthAction)
    float newMlWeightInfluence = f_applyRLAction(currentMlWeightInfluence, 0.0, 1.0, mlWeightInfluenceAction)

    if currentReward < 0
        float targetBalance = 1.0
        float currentSum = newMlWeightInfluence + newDynamicWeightStrength
        if currentSum > targetBalance * 1.2
            float reduction = (currentSum - targetBalance) * rlHarmonizationStrength / 2
            newMlWeightInfluence := math.max(0.2, newMlWeightInfluence - reduction)
            newDynamicWeightStrength := math.max(0.2, newDynamicWeightStrength - reduction)

    float avgValue = (newVolatilityImpact + newDynamicWeightStrength + newMlWeightInfluence) / 3
    float diversityTarget = 0.15 * rlHarmonizationStrength

    float variance = ((math.pow(newVolatilityImpact - avgValue, 2) +
                     math.pow(newDynamicWeightStrength - avgValue, 2) +
                     math.pow(newMlWeightInfluence - avgValue, 2)) / 3)

    if variance < diversityTarget
        float pushFactor = math.sqrt(diversityTarget / math.max(variance, 0.001)) * rlHarmonizationStrength
        newVolatilityImpact := avgValue + (newVolatilityImpact - avgValue) * pushFactor
        newDynamicWeightStrength := avgValue + (newDynamicWeightStrength - avgValue) * pushFactor
        newMlWeightInfluence := avgValue + (newMlWeightInfluence - avgValue) * pushFactor

    newVolatilityImpact := math.max(0.1, math.min(0.9, newVolatilityImpact))
    newDynamicWeightStrength := math.max(0.1, math.min(0.9, newDynamicWeightStrength))
    newMlWeightInfluence := math.max(0.1, math.min(0.9, newMlWeightInfluence))

    float harmonizationScore = 1.0 - math.sqrt(
         math.pow(variance - diversityTarget, 2) * 4)

    float newExplorationRate = math.max(0.01, currentExplorationRate * rlDecayRate)

    [newVolatilityImpact, newDynamicWeightStrength, newMlWeightInfluence, newExplorationRate, harmonizationScore]

//────────────────────────────────────────────────────────────
// NEW HELPERS – standardisation & ring-buffer handling
//────────────────────────────────────────────────────────────
// Simplified standardization helper
f_zscore(val, avg, stdev) =>
    if stdev > 0
        math.max(-2, math.min(2, (val - avg) / stdev))  // Cap at ±2 std devs
    else
        0.0

f_ring_push(arr, v) =>
    if array.size(arr) < mlLookback
        array.push(arr, v)
    else
        array.set(arr, (bar_index) % mlLookback, v)

//────────────────────────────────────────────────────────────
// FUNCTIONS: MACHINE LEARNING & WEIGHT OPTIMIZATION (FIXED)
//────────────────────────────────────────────────────────────
// Structure to store historical performance data for ML
var float[] macdPerformance = array.new_float(0)
var float[] rsiPerformance = array.new_float(0)
var float[] dmiPerformance = array.new_float(0)
var float[] priceChanges = array.new_float(0)
var float[] mlWeights = array.new_float(3, 1.0)  // [macd, rsi, dmi]

// Global variables for ML state
var int g_mlUpdateCounter = 0
var float g_mlAccuracy = 50.0
//────────────────────────────────────────────────────────────
// RUNTIME STATE – single-bar persistent (series) variables
//────────────────────────────────────────────────────────────
// —— Reinforcement-Learning bookkeeping ——
var float  g_rlCurrentReward              = 0.0
var float  g_rlCumulativeReward           = 0.0
var int    g_rlUpdateCounter              = 0           // bars since last RL update
var float[] g_rlRewardHistory             = array.new_float(0)

// —— Optimised parameters (initialised with user inputs) ——
var float  g_rlOptimizedVolatilityImpact      = volatilityImpact
var float  g_rlOptimizedDynamicWeightStrength = dynamicWeightStrength
var float  g_rlOptimizedMlWeightInfluence     = mlWeightInfluence
var float  g_rlCurrentExplorationRate         = rlExplorationRate
var float  g_rlHarmonizationScore             = 0.0

// —— Perfect-Trail runtime info ——
var bool   g_perfectTrailTrend            = true     // current PT trend (true = bull)
var float  g_perfectTrailStrength         = 0.0
var bool   g_perfectTrailTrendChange      = false
var bool   g_perfectTrailSignal           = false
var string g_perfectTrailSignalType       = "none"

var float[] g_rlPredictionHistory  = array.new_float(0)
var float[] g_rlActualHistory      = array.new_float(0)

// Initialize arrays if needed (moved to global scope)
if array.size(macdPerformance) == 0
    macdPerformance := array.new_float(mlLookback, 0.0)
    rsiPerformance := array.new_float(mlLookback, 0.0)
    dmiPerformance := array.new_float(mlLookback, 0.0)
    priceChanges := array.new_float(mlLookback, 0.0)

// Calculate correlation between indicator and future price movement (FIXED)
f_calcCorrelation(float[] x, float[] y, int length) =>
    float sumX = 0.0
    float sumY = 0.0
    float sumXY = 0.0
    float sumX2 = 0.0
    float sumY2 = 0.0

    for i = 0 to length - 1
        if i < array.size(x) and i < array.size(y)
            float xVal = array.get(x, i)
            float yVal = array.get(y, i)
            sumX := sumX + xVal
            sumY := sumY + yVal
            sumXY := sumXY + xVal * yVal
            sumX2 := sumX2 + math.pow(xVal, 2)
            sumY2 := sumY2 + math.pow(yVal, 2)

    float n = length
    float numerator = n * sumXY - sumX * sumY
    float denominator = math.sqrt((n * sumX2 - math.pow(sumX, 2)) * (n * sumY2 - math.pow(sumY, 2)))

    denominator != 0 ? numerator / denominator : 0

// Update performance history using global variable directly (FIXED)
f_updatePerformanceHistory(float macdScore, float rsiScore, float dmiScore) =>
    // Remove oldest and add newest (shift array approach)
    if array.size(macdPerformance) >= mlLookback
        array.remove(macdPerformance, 0)
        array.remove(rsiPerformance, 0)
        array.remove(dmiPerformance, 0)
        array.remove(priceChanges, 0)
    
    // Add new values
    array.push(macdPerformance, macdScore)
    array.push(rsiPerformance, rsiScore)
    array.push(dmiPerformance, dmiScore)
    
    // Calculate past price change (no look-ahead)
    float pastChange = 0.0
    int lookbackPeriod = 5
    if bar_index >= lookbackPeriod
        pastChange := (close - close[lookbackPeriod]) / close[lookbackPeriod] * 100
    
    array.push(priceChanges, pastChange)

// Enhanced ML weight calculation (FIXED)
f_checkMLUpdate(float rawMacdScore, float rawRsiScore, float rawDmiScore) =>
    int arraySize = array.size(macdPerformance)
    if arraySize < 10  // Need minimum data
        [1.0, 1.0, 1.0, 50.0]
    else
        // Calculate correlations
        float macdCorr = f_calcCorrelation(macdPerformance, priceChanges, arraySize)
        float rsiCorr = f_calcCorrelation(rsiPerformance, priceChanges, arraySize)
        float dmiCorr = f_calcCorrelation(dmiPerformance, priceChanges, arraySize)
        
        float macdCorrAbs = math.abs(macdCorr)
        float rsiCorrAbs = math.abs(rsiCorr)
        float dmiCorrAbs = math.abs(dmiCorr)
        
        float totalCorr = macdCorrAbs + rsiCorrAbs + dmiCorrAbs
        
        float newMacdWeight = 1.0
        float newRsiWeight = 1.0
        float newDmiWeight = 1.0
        float newAccuracy = 50.0
        
        if totalCorr > 0.01
            newMacdWeight := macdCorrAbs / totalCorr * 3
            newRsiWeight := rsiCorrAbs / totalCorr * 3
            newDmiWeight := dmiCorrAbs / totalCorr * 3
            
            // Calculate accuracy
            int correctPredictions = 0
            int totalPredictions = 0
            
            for i = 0 to arraySize - 1
                float priceChange = array.get(priceChanges, i)
                
                float macdPred = array.get(macdPerformance, i) * math.sign(macdCorr) * 2
                float rsiPred = array.get(rsiPerformance, i) * math.sign(rsiCorr) * 2
                float dmiPred = array.get(dmiPerformance, i) * math.sign(dmiCorr) * 2
                
                float weightedPred = (macdPred * array.get(mlWeights, 0) +
                                     rsiPred * array.get(mlWeights, 1) +
                                     dmiPred * array.get(mlWeights, 2)) / 3
                
                if math.sign(weightedPred) == math.sign(priceChange) and math.abs(priceChange) > 0.1
                    correctPredictions := correctPredictions + 1
                
                totalPredictions := totalPredictions + 1
            
            if totalPredictions > 0
                newAccuracy := correctPredictions / totalPredictions * 100
        
        [newMacdWeight, newRsiWeight, newDmiWeight, newAccuracy]

// Get ML-optimized weights
f_getMLWeights() =>
    [array.get(mlWeights, 0), array.get(mlWeights, 1), array.get(mlWeights, 2), g_mlAccuracy]

//────────────────────────────────────────────────────────────
// FUNCTIONS: ADAPTIVE LOOKBACK & MARKET REGIME
//────────────────────────────────────────────────────────────
// Fixed scaling factor (no adaptive scaling)
f_getAdaptiveScale(float normalizedVolatility) =>
    1.0  // No adaptation - always return 1.0

// Simplified market regime detection (always returns fixed values)
f_detectMarketRegime(int lookback) =>
    bool isTrending = false
    float dirEff = 0.5
    string marketPhase = 'UNKNOWN'

    // Calculate a simple directional efficiency for compatibility
    float priceChange = close - close[lookback]
    float absChange = math.abs(priceChange)
    float sumAbsChanges = 0.0

    for i = 0 to lookback - 1
        sumAbsChanges := sumAbsChanges + math.abs(close[i] - close[i+1])

    // Basic directional efficiency calculation
    dirEff := absChange / math.max(sumAbsChanges, 0.0001)

    // Return fixed values - no market regime detection
    [isTrending, dirEff, marketPhase]

//────────────────────────────────────────────────────────────
// Dynamic weights (always returns [series float, series float, series float])
//────────────────────────────────────────────────────────────
f_dynamicWeights(bool isTrending, float dirEff) =>
    // declare result holders (series-float by default)
    float wMacd = na
    float wRsi  = na
    float wDmi  = na

    if not useDynamicWeights
        // simply echo the user-inputs (cast to float so they are series, not input)
        wMacd := float(macdWeight)
        wRsi  := float(rsiWeight)
        wDmi  := float(dmiWeight)
    else
        // base weights
        float baseM = float(macdWeight)
        float baseR = float(rsiWeight)
        float baseD = float(dmiWeight)

        // strength of the adjustment
        float effStr = useRL ? g_rlOptimizedDynamicWeightStrength : dynamicWeightStrength

        // directional-efficiency adjustment
        wMacd := baseM * (1 + (dirEff - 0.5) * effStr)
        wRsi  := baseR * (1 - (dirEff - 0.5) * effStr)
        wDmi  := baseD * (1 + (dirEff - 0.5) * effStr * 0.5)

    [wMacd, wRsi, wDmi]   // <- single, well-defined return tuple

//────────────────────────────────────────────────────────────
// SENTIMENT ANALYSIS FOR CRYPTO MARKETS
//────────────────────────────────────────────────────────────
// Sentiment analysis function for crypto markets
f_analyzeCryptoSentiment() =>
    // Base sentiment indicators for crypto markets

    // 1. Volume-based sentiment (sudden volume spikes often indicate sentiment shifts)
    float volumeRatio = volume / ta.sma(volume, 20)
    float volumeSentiment = volumeRatio > 2.0 ? 2.0 :
                           volumeRatio > 1.5 ? 1.0 :
                           volumeRatio < 0.5 ? -1.0 : 0.0

    // 2. Price action sentiment (large candles indicate strong sentiment)
    float bodySize = math.abs(close - open) / ta.atr(14)
    float wickRatio = high != low ? math.abs(high - low - math.abs(close - open)) / math.abs(high - low) : 0

    // Large bullish candle with small wicks = strong positive sentiment
    float candleSentiment = close > open and bodySize > 0.8 and wickRatio < 0.3 ? 2.0 :
                           close > open and bodySize > 0.5 ? 1.0 :
                           close < open and bodySize > 0.8 and wickRatio < 0.3 ? -2.0 :
                           close < open and bodySize > 0.5 ? -1.0 : 0.0

    // 3. Momentum-based sentiment (acceleration in price movement indicates sentiment shift)
    float momentumChange = ta.change(ta.rsi(close, 14), 3)
    float momentumSentiment = momentumChange > 15 ? 2.0 :
                             momentumChange > 8 ? 1.0 :
                             momentumChange < -15 ? -2.0 :
                             momentumChange < -8 ? -1.0 : 0.0

    // 4. Volatility-based sentiment (increasing volatility often precedes sentiment shifts)
    float volatilityChange = ta.change(ta.atr(14) / close * 100, 5)
    float volatilitySentiment = volatilityChange > 1.0 and close > open ? 1.0 :
                               volatilityChange > 1.0 and close < open ? -1.0 : 0.0

    // 5. Combine sentiment indicators with weights
    float rawSentiment = (volumeSentiment * 0.3) +
                         (candleSentiment * 0.3) +
                         (momentumSentiment * 0.25) +
                         (volatilitySentiment * 0.15)

    // 6. Smooth sentiment over lookback period
    var float[] sentimentHistory = array.new_float(sentimentLookback, 0.0)

    // Update sentiment history
    for i = 0 to sentimentLookback - 2
        array.set(sentimentHistory, i, array.get(sentimentHistory, i + 1))
    array.set(sentimentHistory, sentimentLookback - 1, rawSentiment)

    // Calculate weighted average (more weight to recent sentiment)
    float weightedSentiment = 0.0
    float weightSum = 0.0
    for i = 0 to sentimentLookback - 1
        float weight = (i + 1) / sentimentLookback
        weightedSentiment := weightedSentiment + (array.get(sentimentHistory, i) * weight)
        weightSum := weightSum + weight

    // Normalize
    float normalizedSentiment = weightedSentiment / weightSum

    // Apply volatility filter if enabled
    if sentimentVolatilityFilter
        float currentVolatility = ta.atr(14) / close * 100
        float avgVolatility = ta.sma(ta.atr(14) / close * 100, 50)

        // Reduce sentiment impact during high volatility periods
        if currentVolatility > avgVolatility * 1.5
            normalizedSentiment := normalizedSentiment * 0.5

    // Return sentiment score (-2 to +2 range)
    normalizedSentiment

//────────────────────────────────────────────────────────────
// FUNCTIONS: VOLUME ANALYSIS & SWING POINT DETECTION
//────────────────────────────────────────────────────────────
// Volume analysis function
f_analyzeVolume() =>
    // Calculate On-Balance Volume
    float obv = ta.obv
    float smoothedOBV = ta.ema(obv, obvPeriod)

    // Calculate OBV momentum and divergence
    float obvMomentum = ta.change(smoothedOBV, 5) / math.max(math.abs(smoothedOBV), 0.001) * 100

    // Calculate volume trend (increasing or decreasing volume)
    float volumeMA = ta.sma(volume, 20)
    float volumeTrend = volume / math.max(volumeMA, 0.001) - 1

    // Calculate volume-price relationship
    float priceChange = close - close[5]
    float volumeScore = 0.0

    // Positive volume score when price and volume move in same direction
    if priceChange > 0 and volumeTrend > 0
        volumeScore := volumeTrend * 5  // Bullish with increasing volume
    else if priceChange < 0 and volumeTrend > 0
        volumeScore := -volumeTrend * 5  // Bearish with increasing volume
    else if priceChange > 0 and volumeTrend < 0
        volumeScore := volumeTrend * 2  // Bullish with decreasing volume (weaker)
    else if priceChange < 0 and volumeTrend < 0
        volumeScore := -volumeTrend * 2  // Bearish with decreasing volume (weaker)

    // Check for OBV divergence
    bool bullishDivergence = ta.falling(low, volumeDivergenceLength) and ta.rising(smoothedOBV, volumeDivergenceLength)
    bool bearishDivergence = ta.rising(high, volumeDivergenceLength) and ta.falling(smoothedOBV, volumeDivergenceLength)

    if bullishDivergence
        volumeScore := volumeScore + 3
    if bearishDivergence
        volumeScore := volumeScore - 3

    [volumeScore, bullishDivergence, bearishDivergence]

// Swing point detection function (Corrected and Simplified)
f_detectSwingPoints() =>
    // Use built-in pivot functions for accuracy and performance
    float swingHighValue = ta.pivothigh(high, swingStrength, swingStrength)
    float swingLowValue  = ta.pivotlow(low, swingStrength, swingStrength)

    bool isSwingHigh = not na(swingHighValue)
    bool isSwingLow  = not na(swingLowValue)

    // Analyze swing point sequence for trend confirmation
    // Store last two swing highs and lows
    var float lastHigh1 = na, var float lastHigh2 = na
    var float lastLow1 = na, var float lastLow2 = na

    if isSwingHigh
        lastHigh2 := lastHigh1
        lastHigh1 := swingHighValue
    if isSwingLow
        lastLow2 := lastLow1
        lastLow1 := swingLowValue

    // Determine trend based on swing points
    bool higherHighs = not na(lastHigh1) and not na(lastHigh2) and lastHigh1 > lastHigh2
    bool higherLows  = not na(lastLow1) and not na(lastLow2) and lastLow1 > lastLow2
    bool lowerHighs  = not na(lastHigh1) and not na(lastHigh2) and lastHigh1 < lastHigh2
    bool lowerLows   = not na(lastLow1) and not na(lastLow2) and lastLow1 < lastLow2

    // Determine swing-based trend
    string swingTrend = "NEUTRAL"
    if higherHighs and higherLows
        swingTrend := "UPTREND"
    else if lowerHighs and lowerLows
        swingTrend := "DOWNTREND"
    else if higherLows and lowerHighs
        swingTrend := "ACCUMULATION"
    else if lowerLows and higherHighs
        swingTrend := "DISTRIBUTION"

    // Calculate swing strength score (-5 to +5)
    float swingScore = 0.0
    if swingTrend == "UPTREND"
        swingScore := 5.0
    else if swingTrend == "DOWNTREND"
        swingScore := -5.0
    else if swingTrend == "ACCUMULATION"
        swingScore := 2.0
    else if swingTrend == "DISTRIBUTION"
        swingScore := -2.0

    [swingScore, swingTrend, isSwingHigh, isSwingLow, swingHighValue, swingLowValue]

// Multi-Timeframe Analysis function
// Normalize score function (moved here to be defined before use)
f_normalizeScore(score, minScore, maxScore) =>
    epsilon = 0.00001
    math.max(math.min((score - minScore) / math.max(maxScore - minScore, epsilon) * 100, 100), 0)

// Simple trend score calculation function
f_simpleTrendScore() =>
    // Calculate a simplified trend score using basic indicators
    [macdLine, signalLine, histValue] = ta.macd(close, 12, 26, 9)
    float rsiValue = ta.rsi(close, 14)
    float atrValue = ta.atr(14)
    [diplus, diminus, _] = ta.dmi(14, 25)

    // Normalize and combine scores
    float macdScore = histValue / atrValue
    float rsiScore = (rsiValue - 50) / 50
    float dmiScore = (diplus - diminus) / 100

    // Return normalized combined score
    f_normalizeScore(macdScore + rsiScore + dmiScore, -7, 7)

// Helper function to determine market condition based on trend score
f_getSimpleMarketCondition(float score) =>
    score > 60 ? "TRENDING_UP" : score < 40 ? "TRENDING_DOWN" : "RANGING"

// Fixed MTF alignment with proper lookahead prevention
f_calculateTFAlignment(float currentTrendScore, string higherTF, string lowerTF) =>
    // Get trend scores with lookahead protection
    float higherTFScore = request.security(syminfo.tickerid, higherTF, f_simpleTrendScore(), lookahead=barmerge.lookahead_off)
    float lowerTFScore = request.security(syminfo.tickerid, lowerTF, f_simpleTrendScore(), lookahead=barmerge.lookahead_off)

    // Use sign agreement instead of raw distance for better alignment logic
    float higherTFSignAgreement = math.sign(currentTrendScore - 50) == math.sign(higherTFScore - 50) ? 100 : 0
    float lowerTFSignAgreement = math.sign(currentTrendScore - 50) == math.sign(lowerTFScore - 50) ? 100 : 0
    
    // Weight agreement by magnitude for better quality assessment
    float higherTFMagnitude = math.abs(higherTFScore - 50) / 50
    float lowerTFMagnitude = math.abs(lowerTFScore - 50) / 50
    
    float weightedHigherAlignment = higherTFSignAgreement * (0.5 + higherTFMagnitude * 0.5)
    float weightedLowerAlignment = lowerTFSignAgreement * (0.5 + lowerTFMagnitude * 0.5)
    
    float alignmentScore = weightedHigherAlignment * 0.7 + weightedLowerAlignment * 0.3
    
    // Dynamic weighting based on alignment quality
    float dynamicWeight = mtfWeight * (alignmentScore / 100)
    
    // Calculate weighted trend score
    float weightedTrendScore = currentTrendScore * (1 - dynamicWeight) + higherTFScore * dynamicWeight
    
    // Apply trend momentum with lookahead protection
    float higherTFMomentum = request.security(syminfo.tickerid, higherTF, ta.change(close, 5) / close * 100, lookahead=barmerge.lookahead_off)
    weightedTrendScore := weightedTrendScore * (1 + math.sign(higherTFMomentum) * 0.05)
    
    [alignmentScore, weightedTrendScore]

//────────────────────────────────────────────────────────────
// FUNCTIONS: MARKET CONDITION & TECHNICAL INDICATORS
//────────────────────────────────────────────────────────────
f_detectMarketCondition(tf) =>
    var bool nweUsed = true
    var bool isTrending = false
    if tf == 'W' or tf == 'M' or tf == '3M' or tf == '12M'
        if na(upper) or na(lower)
            nweUsed := false
            isTrending := true
        else
            isTrending := close > upper or close < lower
    else
        isTrending := close > upper or close < lower
    [isTrending ? 'TRENDING' : 'RANGING', nweUsed]

// CORE INDICATOR ENGINE -- simplified and fixed
f_calcIndicators(string tf, bool isMtfCtx) =>
    // Basic volatility
    float vol = ta.atr(14) / close * 100
    float volSmth = ta.ema(vol, volMAPeriod)
    float volNorm = volSmth / nz(ta.sma(volSmth, volatilityLookback), 1)

    [_, dirEff, mPhase] = f_detectMarketRegime(50)

    // Volume and swing analysis
    float volScore = 0.0
    bool bullVolDiv = false, bearVolDiv = false, isSwHi = false, isSwLo = false
    if useVolumeAnalysis
        [volScore, bullVolDiv, bearVolDiv] = f_analyzeVolume()
    
    string swTrend = "NEUTRAL"
    float swingScore = 0.0
    if useSwingPoints
        [swingScore, swTrend, isSwHi, isSwLo, _, _] = f_detectSwingPoints()

    // Classic indicators
    [macdL, macdS, macdH] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
    float rsiVal = ta.rsi(close, rsiLength)
    [diP, diM, adx] = ta.dmi(dmiLength, adxThreshold)
    bool adxStrong = adx > adxThreshold
    
    [mCond, nweUsed] = f_detectMarketCondition(tf)
    float nweScore = 0.0
    if nweUsed and mae != 0
        nweScore := (close - out) / mae

    // Simplified component calculation
    float zMacd = ta.atr(14) != 0 ? f_zscore(macdH / ta.atr(14), 0, 0.5) : 0
    float zRsi = (rsiVal - 50) / 50
    float zDmi = f_zscore((diP - diM) / 100 * (adxStrong ? 1 : 0.5), 0, 0.3)
    float zVol = f_zscore(volScore / 10, 0, 0.4)
    float zSw = f_zscore(swingScore / 5, 0, 0.4)

    // Dynamic weights
    [wM, wR, wD] = f_dynamicWeights(mCond == "TRENDING", dirEff)
    
    // ML variables with defaults
    float mlM = 1.0
    float mlR = 1.0  
    float mlD = 1.0
    float mlAcc = 50.0
    
    if useML
        [mlM, mlR, mlD, mlAcc] = f_getMLWeights()
        float alphaWeight = useRL ? g_rlOptimizedMlWeightInfluence : mlWeightInfluence
        wM := wM * (1 - alphaWeight) + mlM * alphaWeight
        wR := wR * (1 - alphaWeight) + mlR * alphaWeight
        wD := wD * (1 - alphaWeight) + mlD * alphaWeight

    // Compose final score
    float base = (useMacd ? zMacd * wM : 0) + (useRsi ? zRsi * wR : 0) + (useDmi ? zDmi * wD : 0)
    if useVolumeAnalysis
        base := base + zVol * volumeImpact
    if useSwingPoints
        base := base + zSw * swingImpact

    // MTF blend
    float mtfAlign = 100.0
    float trendSc = base
    if mtfEnabled and not isMtfCtx
        [mtfAlign, trendSc] = f_calculateTFAlignment(base, higherTF, lowerTF)
        if mtfAlign < mtfAlignmentThreshold
            trendSc := base

    [close, ta.ema(close, 20), ta.ema(close, 50), ta.ema(close, 200),
     trendSc, rsiVal, adx, volSmth, volNorm,
     mCond, wM, ta.rsi(close, rsiPeriod1), ta.rsi(close, rsiPeriod2),
     ta.sma(ta.stoch(rsiVal, rsiVal, rsiVal, stochLength), smoothK),
     ta.sma(ta.sma(ta.stoch(rsiVal, rsiVal, rsiVal, stochLength), smoothK), smoothD),
     nweUsed, nweScore, dirEff,
     rsiLength, macdFastLength, macdSlowLength, dmiLength,
     mlM, mlR, mlD, mlAcc, volScore, swingScore, mtfAlign,
     mPhase, swTrend, bullVolDiv, bearVolDiv, isSwHi, isSwLo]

// Fixed confluence system with proper component inputs
f_calculateTrendConfluence(float macdScore, float rsiScore, float dmiScore, float volumeScore, float swingScore) =>
    float confluenceThreshold = 0.3 // Reduced threshold for better sensitivity
    
    // All scores are already standardized to similar ranges
    // Calculate directional agreement with proper thresholds
    int bullishSignals = (macdScore > 0.2 ? 1 : 0) + (rsiScore > 0.2 ? 1 : 0) + 
                         (dmiScore > 0.2 ? 1 : 0) + (volumeScore > 0.2 ? 1 : 0) + 
                         (swingScore > 0.2 ? 1 : 0)
    
    int bearishSignals = (macdScore < -0.2 ? 1 : 0) + (rsiScore < -0.2 ? 1 : 0) + 
                         (dmiScore < -0.2 ? 1 : 0) + (volumeScore < -0.2 ? 1 : 0) + 
                         (swingScore < -0.2 ? 1 : 0)
    
    // Calculate confluence score (0-1)
    float confluenceScore = math.max(bullishSignals, bearishSignals) / 5.0
    
    // Determine trend direction and confirmation
    int trendDirection = bullishSignals > bearishSignals ? 1 : bearishSignals > bullishSignals ? -1 : 0
    bool confirmedTrend = confluenceScore >= confluenceThreshold
    
    // Calculate weighted average of standardized scores
    float avgScore = (macdScore + rsiScore + dmiScore + volumeScore + swingScore) / 5
    
    [confluenceScore, trendDirection, confirmedTrend, avgScore]

// HIGH PRIORITY IMPROVEMENT: Trend Momentum Analysis
f_trendMomentumAnalysis(trendScore) =>
    // Calculate trend momentum over different periods
    float momentum1 = ta.change(trendScore, 1)
    float momentum3 = ta.change(trendScore, 3)
    float momentum5 = ta.change(trendScore, 5)
    
    // Calculate trend acceleration (change in momentum)
    float acceleration = ta.change(momentum3, 1)
    
    // Trend consistency (how steady the trend has been) - lower stdev = more consistent
    float trendStdev = ta.stdev(trendScore, 10)
    float trendConsistency = math.max(0, 1 - trendStdev / 10)
    
    // Momentum strength classification
    string momentumStrength = "Weak"
    if math.abs(momentum3) > 5
        momentumStrength := "Strong"
    else if math.abs(momentum3) > 2
        momentumStrength := "Moderate"
    
    // Trend quality score (combines strength, momentum, and consistency)
    float trendStrengthComponent = math.min(math.abs(trendScore - 50) / 50, 1) * 0.4
    float momentumComponent = math.min(math.abs(momentum3) / 5, 1) * 0.3
    float consistencyComponent = trendConsistency * 0.3
    
    float trendQuality = trendStrengthComponent + momentumComponent + consistencyComponent
    
    // Adjust trend score based on momentum and quality
    float momentumAdjustment = math.sign(momentum3) * math.min(math.abs(momentum3) / 2, 3)
    float qualityAdjustedScore = trendScore + momentumAdjustment * trendQuality
    
    [qualityAdjustedScore, trendQuality, momentumStrength, acceleration, trendConsistency]

// Standardize individual components before weighting (SIMPLIFIED)
f_standardizeComponent(float value, float lookback) =>
    float mean = ta.sma(value, int(lookback))
    float stdev = ta.stdev(value, int(lookback))
    if stdev > 0
        math.max(-2, math.min(2, (value - mean) / stdev))  // Cap at ±2 std devs
    else
        0.0

// Enhanced component calculation with proper scaling (SIMPLIFIED)
f_calcStandardizedComponents() =>
    // Calculate raw components
    [macdLine, signalLine, histLine] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
    float rawMacdScore = ta.atr(14) != 0 ? histLine / ta.atr(14) : 0
    float rawRsiScore = (ta.rsi(close, rsiLength) - 50) / 50
    [diplus, diminus, adx] = ta.dmi(dmiLength, adxThreshold)
    float rawDmiScore = (diplus - diminus) / 100 * (adx > adxThreshold ? 1 : 0.5)
    
    // Calculate volume score
    float rawVolumeScore = 0.0
    if useVolumeAnalysis
        [volumeScore, bullishVolDiv, bearishVolDiv] = f_analyzeVolume()
        rawVolumeScore := volumeScore / 5
    
    // Calculate swing score
    float rawSwingScore = 0.0
    if useSwingPoints
        [swingScore, swingTrend, isSwingHigh, isSwingLow, swingHighValue, swingLowValue] = f_detectSwingPoints()
        rawSwingScore := swingScore / 5
    
    // Simple standardization using fixed parameters
    float stdMacdScore = f_zscore(rawMacdScore, 0, 0.5)
    float stdRsiScore = rawRsiScore  // Already normalized
    float stdDmiScore = f_zscore(rawDmiScore, 0, 0.3)
    float stdVolumeScore = f_zscore(rawVolumeScore, 0, 0.4)
    float stdSwingScore = f_zscore(rawSwingScore, 0, 0.4)
    
    [stdMacdScore, stdRsiScore, stdDmiScore, stdVolumeScore, stdSwingScore, rawMacdScore, rawRsiScore, rawDmiScore]

// Fixed trend score calculation with proper normalization and symmetric thresholds
f_calcTrendScore(trendScore, smoothedVol, normVol, dirEff) =>
    // Get standardized components
    [stdMacdScore, stdRsiScore, stdDmiScore, stdVolumeScore, stdSwingScore, rawMacdScore, rawRsiScore, rawDmiScore] = f_calcStandardizedComponents()
    
    // Get ML weights if enabled
    float mlMacdWeight = 1.0, float mlRsiWeight = 1.0, float mlDmiWeight = 1.0
    if useML
        [mlMacdWeight, mlRsiWeight, mlDmiWeight, mlAccuracy] = f_getMLWeights()
    
    // Apply dynamic weights
    [dynMacdWeight, dynRsiWeight, dynDmiWeight] = f_dynamicWeights(false, dirEff)
    
    // Use RL-optimized weights if enabled
    float effectiveMlWeightInfluence = useRL ? g_rlOptimizedMlWeightInfluence : mlWeightInfluence
    
    float finalMacdWeight = useML ? dynMacdWeight * (1 - effectiveMlWeightInfluence) + mlMacdWeight * effectiveMlWeightInfluence : dynMacdWeight
    float finalRsiWeight = useML ? dynRsiWeight * (1 - effectiveMlWeightInfluence) + mlRsiWeight * effectiveMlWeightInfluence : dynRsiWeight
    float finalDmiWeight = useML ? dynDmiWeight * (1 - effectiveMlWeightInfluence) + mlDmiWeight * effectiveMlWeightInfluence : dynDmiWeight
    
    // Calculate weighted standardized score
    float weightedScore = (stdMacdScore * finalMacdWeight + stdRsiScore * finalRsiWeight + stdDmiScore * finalDmiWeight) / 
                         (finalMacdWeight + finalRsiWeight + finalDmiWeight)
    
    // Add volume and swing impacts if enabled
    if useVolumeAnalysis
        weightedScore := weightedScore * (1 - volumeImpact) + stdVolumeScore * volumeImpact
    if useSwingPoints
        weightedScore := weightedScore + stdSwingScore * swingImpact
    
    // Apply confluence analysis with proper components
    [confluenceScore, trendDirection, confirmedTrend, avgConfluenceScore] = f_calculateTrendConfluence(
         stdMacdScore, stdRsiScore, stdDmiScore, stdVolumeScore, stdSwingScore)
    
    // Apply momentum analysis
    [momentumAdjustedScore, trendQuality, momentumStrength, acceleration, trendConsistency] = f_trendMomentumAnalysis(weightedScore)
    
    // Convert standardized score to 0-100 range
    // Map ±3 standard deviations to 0-100 range with 50 as neutral
    float normalizedScore = 50 + (momentumAdjustedScore / 3) * 50
    normalizedScore := math.max(0, math.min(100, normalizedScore))
    
    // Apply confluence and quality filtering (more lenient)
    float confluenceMultiplier = 0.8 + (confluenceScore * 0.2)  // Range: 0.8 to 1.0
    float confluenceAdjustedScore = 50 + (normalizedScore - 50) * confluenceMultiplier
    
    float qualityMultiplier = 0.9 + (trendQuality * 0.1)  // Range: 0.9 to 1.0
    float finalNormalizedScore = 50 + (confluenceAdjustedScore - 50) * qualityMultiplier
    
    // Ensure score stays within bounds
    finalNormalizedScore := math.max(0, math.min(100, finalNormalizedScore))
    
    // Calculate symmetric thresholds
    float baseThreshold = 50
    
    // Use RL-optimized volatility impact if enabled
    float effectiveVolatilityImpact = useRL ? g_rlOptimizedVolatilityImpact : volatilityImpact
    
    // Constrained volatility adjustment to keep thresholds reasonable
    float volatilityAdjustment = math.max(0, math.min(5, math.pow(normVol - 1, 2) * effectiveVolatilityImpact * 3))
    
    // Symmetric dynamic thresholds
    float bullishDynamicThreshold = math.min(55, baseThreshold + volatilityAdjustment)
    float bearishDynamicThreshold = math.max(45, baseThreshold - volatilityAdjustment)
    
    // Use the wider threshold for both directions to maintain symmetry
    float dynamicThreshold = math.max(bullishDynamicThreshold, 100 - bearishDynamicThreshold)
    
    // Strong thresholds with quality adjustment
    float qualityAdjustment = (1 - trendQuality) * 2
    float confluenceAdjustment = (1 - confluenceScore) * 1
    
    float strongThreshold = math.min(75, dynamicThreshold + 15 + qualityAdjustment + confluenceAdjustment)
    
    [finalNormalizedScore, dynamicThreshold, strongThreshold, confluenceScore, trendQuality, confirmedTrend, momentumStrength]

f_getColor(score, dynThreshold, strongThreshold) =>
    // FIX: Color logic is now symmetric for bullish and bearish conditions.
    na(score) ? color.rgb(150, 150, 150) :
     score >= strongThreshold ? color.rgb(0, 255, 0) : // Strong Bull
     score >= (strongThreshold + dynThreshold) / 2 ? color.rgb(0, 220, 0) : // Moderate Bull
     score >= dynThreshold ? color.rgb(0, 180, 0) : // Bull
     score > 52 ? color.rgb(150, 255, 150) : // Weak Bull
     score >= 48 ? color.rgb(200, 200, 200) : // Neutral
     score > 100 - dynThreshold ? color.rgb(255, 150, 150) : // Weak Bear
     score > (100 - strongThreshold + 100 - dynThreshold) / 2 ? color.rgb(180, 0, 0) : // Bear
     score > 100 - strongThreshold ? color.rgb(220, 0, 0) : // Moderate Bear
     color.rgb(255, 0, 0) // Strong Bear

f_getTrendStrength(score, dynThreshold, strongThreshold) =>
    var strengthLevels = array.from('Super Bullish', 'Strong Bullish', 'Moderate Bullish', 'Bullish', 'Weak Bullish', 'Neutral', 'Weak Bearish', 'Bearish')
    strength = score >= strongThreshold + 10 ? array.get(strengthLevels, 0) :
               score >= strongThreshold ? array.get(strengthLevels, 1) :
               score >= (strongThreshold + dynThreshold) / 2 ? array.get(strengthLevels, 2) :
               score >= dynThreshold ? array.get(strengthLevels, 3) :
               score > 52 ? array.get(strengthLevels, 4) :
               score >= 48 ? array.get(strengthLevels, 5) : array.get(strengthLevels, 7)
    localTrendColor = f_getColor(score, dynThreshold, strongThreshold)
    strengthScore = strength == 'Super Bullish' ? 5 :
                     strength == 'Strong Bullish'  ? 4 :
                     strength == 'Moderate Bullish'? 3 :
                     strength == 'Bullish'         ? 2 :
                     strength == 'Weak Bullish'    ? 1 :
                     strength == 'Neutral'         ? 0 :
                     strength == 'Weak Bearish'    ? -1 :
                     strength == 'Bearish'         ? -2 : -5
    [strength, localTrendColor, strengthScore]

f_getMarketConditionText(condition) =>
    condition == 'TRENDING' ? '↗️ Trending' : '↔️ Ranging'

f_formatStrength(strength) =>
    str.tostring(math.round(strength, DECIMAL_PLACES))

f_getTextSize(sizeStr) =>
    switch sizeStr
        'Tiny'   => size.tiny
        'Small'  => size.small
        'Normal' => size.normal
        'Large'  => size.large
        => size.auto

// Get dashboard colors based on selected theme
f_getDashboardColors() =>
    // Default theme colors
    color bgColor = color.new(color.rgb(27, 28, 36), 100 - dashboardOpacity)
    color frameColor = color.rgb(55, 65, 81)
    color borderColor = color.rgb(42, 46, 57)
    color titleBgColor = color.new(color.rgb(47, 73, 150), 20)
    color titleTextColor = color.rgb(240, 240, 245)
    color headerBgColor = color.new(color.rgb(42, 46, 57), 10)
    color headerTextColor = color.rgb(180, 185, 190)
    color rowBgColor = color.new(color.rgb(35, 37, 45), 10)
    color rowTextColor = color.rgb(210, 210, 210)
    color bullColor = color.rgb(0, 179, 122)
    color bearColor = color.rgb(225, 50, 85)
    color neutralColor = color.rgb(180, 180, 180)

    // Apply theme-specific colors
    if dashboardColorTheme == 'Monochrome'
        bgColor := color.new(color.black, 100 - dashboardOpacity)
        frameColor := color.gray
        borderColor := color.white
        titleBgColor := color.new(color.white, 80)
        titleTextColor := color.white
        headerBgColor := color.new(color.gray, 70)
        headerTextColor := color.white
        rowBgColor := color.new(color.black, 10)
        rowTextColor := color.white
        bullColor := color.white
        bearColor := color.gray
        neutralColor := color.silver
    else if dashboardColorTheme == 'High Contrast'
        bgColor := color.new(color.black, 100 - dashboardOpacity)
        frameColor := color.yellow
        borderColor := color.yellow
        titleBgColor := color.new(color.blue, 20)
        titleTextColor := color.yellow
        headerBgColor := color.new(color.black, 10)
        headerTextColor := color.yellow
        rowBgColor := color.new(color.black, 10)
        rowTextColor := color.white
        bullColor := color.lime
        bearColor := color.fuchsia
        neutralColor := color.yellow
    else if dashboardColorTheme == 'Pastel'
        bgColor := color.new(color.rgb(245, 245, 255), 100 - dashboardOpacity)
        frameColor := color.rgb(180, 190, 210)
        borderColor := color.rgb(200, 210, 230)
        titleBgColor := color.new(color.rgb(180, 200, 230), 20)
        titleTextColor := color.rgb(80, 90, 120)
        headerBgColor := color.new(color.rgb(220, 230, 245), 10)
        headerTextColor := color.rgb(100, 110, 140)
        rowBgColor := color.new(color.rgb(235, 240, 250), 10)
        rowTextColor := color.rgb(80, 90, 120)
        bullColor := color.rgb(130, 200, 180)
        bearColor := color.rgb(220, 150, 170)
        neutralColor := color.rgb(180, 180, 200)

    [bgColor, frameColor, borderColor, titleBgColor, titleTextColor,
     headerBgColor, headerTextColor, rowBgColor, rowTextColor,
     bullColor, bearColor, neutralColor]

f_getTrendDirection(tf) =>
    [tfClose, tfEma20, tfEma50, tfEma200, tfTrendScore, tfRsi, tfAdx, tfSmoothedVol, tfNormVol,
     tfMarketCond, tfDynMacdWeight, tfRsi1, tfRsi2, tfStochK, tfStochD, nweUsed, tfNweScore,
     tfDirEff, tfAdaptiveRsiLength, tfAdaptiveMacdFast, tfAdaptiveMacdSlow, tfAdaptiveDmiLength,
     tfMlMacdWeight, tfMlRsiWeight, tfMlDmiWeight, tfMlAccuracy, tfVolumeScore, tfSwingScore, tfMtfAlignmentScore,
     tfMarketPhase, tfSwingTrend, tfBullishVolDiv, tfBearishVolDiv, tfIsSwingHigh, tfIsSwingLow] = 
     request.security(syminfo.tickerid, tf, f_calcIndicators(tf, true), lookahead=barmerge.lookahead_off)

    float tfDirEffSafe = na(tfDirEff) ? 0.5 : tfDirEff
    [tfNormTrendScore, tfDynThreshold, tfStrongThreshold, tfConfluenceScore, tfTrendQuality, tfConfirmedTrend, tfMomentumStrength] = 
         f_calcTrendScore(tfTrendScore, tfSmoothedVol, tfNormVol, tfDirEffSafe)

    [tfStrength, tfTrendColor, tfStrengthScore] = f_getTrendStrength(tfNormTrendScore, tfDynThreshold, tfStrongThreshold)

    [tfStrength, tfTrendColor, tfNormTrendScore, tfMarketCond, tfDynMacdWeight, tfRsi1, tfRsi2, tfStochK, tfStochD, tfStrengthScore, tfMlAccuracy]

//────────────────────────────────────────────────────────────
// FUNCTIONS: REVERSAL SIGNALS & UPDATES (FIXED)
//────────────────────────────────────────────────────────────
// Fixed reversal signals using previous bar data to prevent repainting
f_calculateReversalSignals(normTrendScore, rsi1_val, rsi2_val, stoch_k, stoch_d, nweScore, normVol, dirEff) =>
    // Use current bar values but with enhanced logic to reduce false signals
    float volatilityFactor = normVol
    float regimeFactor = dirEff

    // More adaptive adjustments
    float bullishAdj = 5 * (volatilityFactor > 1.2 ? 1.5 : 1.0) * (regimeFactor < 0.4 ? 1.2 : 1.0)
    float bearishAdj = 5 * (volatilityFactor > 1.2 ? 1.5 : 1.0) * (regimeFactor < 0.4 ? 1.2 : 1.0)

    // Add volume confirmation for stronger signals
    float volumeConfirmation = volume > ta.sma(volume, 20) * 1.2 ? 1.0 : 0.8

    // Enhanced reversal detection with multiple confirmations
    bool veryStrongBullish = normTrendScore > 92 - bullishAdj and
                             rsi2_val > 82 - bullishAdj and
                             stoch_k > 75 - bullishAdj and
                             nweScore > 0.5 * volumeConfirmation and
                             ta.crossover(stoch_k, stoch_d)

    bool veryStrongBearish = normTrendScore < 8 + bearishAdj and
                             rsi2_val < 18 + bearishAdj and
                             stoch_k < 25 + bearishAdj and
                             nweScore < -0.5 * volumeConfirmation and
                             ta.crossunder(stoch_k, stoch_d)

    // Extreme signals require more confirmations to reduce false signals
    bool extremeBullish = normTrendScore > 95 - bullishAdj and
                         rsi2_val > 90 - bullishAdj and
                         stoch_k > 95 - bullishAdj and
                         stoch_k > stoch_d and
                         nweScore > 1 * volumeConfirmation and
                         ta.change(normTrendScore, 3) > 5

    bool extremeBearish = normTrendScore < 5 + bearishAdj and
                          rsi2_val < 10 + bearishAdj and
                          stoch_k < 5 + bearishAdj and
                          stoch_k < stoch_d and
                          nweScore < -1 * volumeConfirmation and
                          ta.change(normTrendScore, 3) < -5

    [veryStrongBullish, veryStrongBearish, extremeBullish, extremeBearish]

// Fixed NWE crossover detection
detectNWECrossover(tf) =>
    [upperTF, lowerTF] = request.security(syminfo.tickerid, tf, [upper, lower], lookahead=barmerge.lookahead_off)
    closeTF = request.security(syminfo.tickerid, tf, close, lookahead=barmerge.lookahead_off)
    
    // Use previous bar's bands for comparison to avoid repainting
    bullishCrossover = ta.crossover(closeTF, lowerTF)
    bearishCrossover = ta.crossunder(closeTF, upperTF)
    
    [bullishCrossover, bearishCrossover]

// FIX: The sentiment history array is moved to the global scope to prevent re-initialization issues.
var float[] sentimentHistory = array.new_float(0)
if array.size(sentimentHistory) != sentimentLookback
    sentimentHistory := array.new_float(sentimentLookback, 0.0)

// Calculate indicators for the current timeframe with enhanced adaptive features
// FIX: Added 'isMtfContext' parameter to f_calcIndicators to prevent recursion.
[currentClose, currentEma20, currentEma50, currentEma200, currentTrendScore, currentRsi, currentAdx,
 currentSmoothedVolatility, currentNormalizedVolatility, currentLocalMarketCondition, currentDynMacdWeight,
 currentRsi1, currentRsi2, currentStochK, currentStochD, currentNweUsed, currentNweScore,
 currentDirEfficiency, currentAdaptiveRsiLength, currentAdaptiveMacdFast, currentAdaptiveMacdSlow,
 currentAdaptiveDmiLength, currentMlMacdWeight, currentMlRsiWeight, currentMlDmiWeight, currentMlAccuracy,
 currentVolumeScore, currentSwingScore, currentMtfAlignmentScore, currentMarketPhase, currentSwingTrend,
 currentBullishVolDiv, currentBearishVolDiv, currentIsSwingHigh, currentIsSwingLow] = f_calcIndicators(timeframe.period, false) // Not in MTF context

// Handle ML updates in the main script context (FIXED)
if useML
    // Extract raw indicator scores for ML performance tracking
    float rawMacdScore = currentTrendScore / 3  // Approximate the raw score
    float rawRsiScore = (currentRsi - 50) / 50
    float rawDmiScore = currentDirEfficiency - 0.5  // Approximate DMI from directional efficiency

    // Update performance history
    f_updatePerformanceHistory(rawMacdScore, rawRsiScore, rawDmiScore)

    // Update ML weights periodically
    g_mlUpdateCounter := g_mlUpdateCounter + 1
    if g_mlUpdateCounter >= mlUpdateFrequency and array.size(priceChanges) >= mlLookback
        g_mlUpdateCounter := 0

        // Calculate new weights and accuracy
        [newMacdWeight, newRsiWeight, newDmiWeight, newAccuracy] = f_checkMLUpdate(rawMacdScore, rawRsiScore, rawDmiScore)

        // Apply learning rate to smooth weight changes
        float currentMacdWeight = array.get(mlWeights, 0)
        float currentRsiWeight = array.get(mlWeights, 1)
        float currentDmiWeight = array.get(mlWeights, 2)

        array.set(mlWeights, 0, currentMacdWeight * (1 - mlLearningRate) + newMacdWeight * mlLearningRate)
        array.set(mlWeights, 1, currentRsiWeight * (1 - mlLearningRate) + newRsiWeight * mlLearningRate)
        array.set(mlWeights, 2, currentDmiWeight * (1 - mlLearningRate) + newDmiWeight * mlLearningRate)

        // Update accuracy
        g_mlAccuracy := newAccuracy

// Calculate trend score with improved function that includes directional efficiency
float dirEff = na(currentDirEfficiency) ? 0.5 : currentDirEfficiency
[normalizedTrendScore, dynamicThreshold, strongThreshold, confluenceScore, trendQuality, confirmedTrend, momentumStrength] = f_calcTrendScore(
     currentTrendScore, currentSmoothedVolatility, currentNormalizedVolatility, dirEff)

// Handle RL updates in the main script context
if useRL
    // Initialize RL parameters on first run
    if barstate.isfirst
        g_rlCurrentExplorationRate := rlExplorationRate
        g_rlOptimizedVolatilityImpact := volatilityImpact
        g_rlOptimizedDynamicWeightStrength := dynamicWeightStrength
        g_rlOptimizedMlWeightInfluence := mlWeightInfluence

    // FIX: Align prediction and outcome for reward calculation.
    // We evaluate the prediction made 5 bars ago with the actual price change that occurred over those 5 bars.
    // This removes the look-ahead bias from the reward calculation.
    int rewardLookback = 5
    float prediction = (normalizedTrendScore[rewardLookback] - 50) // Prediction from 5 bars ago
    float actual = 0.0

    if bar_index > rewardLookback
        // Actual outcome: % price change over the last 5 bars
        actual := (close - close[rewardLookback]) / close[rewardLookback] * 100

    // Calculate current reward for the action taken 5 bars ago
    g_rlCurrentReward := f_calculateReward(prediction, actual)

    // Initialize arrays if needed
    if array.size(g_rlRewardHistory) == 0
        g_rlRewardHistory := array.new_float(rlRewardWindow, 0.0)

    // Update history arrays
    array.remove(g_rlRewardHistory, 0)
    array.push(g_rlRewardHistory, g_rlCurrentReward)
    
    // Calculate cumulative reward
    g_rlCumulativeReward := f_calculateCumulativeReward(g_rlRewardHistory)

    // Update RL parameters periodically
    g_rlUpdateCounter := g_rlUpdateCounter + 1

    if g_rlUpdateCounter >= rlUpdateFrequency
        g_rlUpdateCounter := 0

        // Update parameters using RL with simplified parameter harmonization
        [newVolatilityImpact, newDynamicWeightStrength, newMlWeightInfluence, newExplorationRate, newHarmonizationScore] =
             f_updateRLParameters(g_rlCumulativeReward, g_rlCurrentExplorationRate, // Use cumulative reward for more stable policy
                               g_rlOptimizedVolatilityImpact,
                               g_rlOptimizedDynamicWeightStrength, g_rlOptimizedMlWeightInfluence)

        // Apply new parameter values
        g_rlOptimizedVolatilityImpact := newVolatilityImpact
        g_rlOptimizedDynamicWeightStrength := newDynamicWeightStrength
        g_rlOptimizedMlWeightInfluence := newMlWeightInfluence
        g_rlCurrentExplorationRate := newExplorationRate
        g_rlHarmonizationScore := newHarmonizationScore

// Get trend strength assessment with enhanced accuracy
[currentStrength, trendColor, currentStrengthScore] = f_getTrendStrength(
     normalizedTrendScore, dynamicThreshold, strongThreshold)

// Reversal Signals
[currentVeryStrongBullishReversal, currentVeryStrongBearishReversal, currentExtremeBullishReversal, currentExtremeBearishReversal] = f_calculateReversalSignals(normalizedTrendScore, currentRsi1, currentRsi2, currentStochK, currentStochD, currentNweScore, currentNormalizedVolatility, currentDirEfficiency)
[currentStrongBullishReversal, currentStrongBearishReversal] = detectNWECrossover(timeframe.period)

updateReversalSignal(extreme, veryStrong, strong, bullish, lastSignal, lastTime, lastColor, tf) =>
    newSignal = lastSignal
    newTime   = lastTime
    newColor  = lastColor
    if extreme or veryStrong or strong
        newSignal := bullish ? (extreme ? 'Extreme Bullish' : veryStrong ? 'Very Strong Bullish' : 'Strong Bullish') :
                               (extreme ? 'Extreme Bearish' : veryStrong ? 'Very Strong Bearish' : 'Strong Bearish')
        newTime   := tf == 'Cur' ? bar_index : time
        newColor  := extreme ? color.purple : veryStrong ? color.blue : color.yellow
    [newSignal, newTime, newColor, tf]

var string lastReversalSignalCurrent = 'None'
var int lastReversalTimeCurrent = 0
var color lastReversalColorCurrent = color.gray

updateReversalSignals() =>
    [newSignalCurrent, newTimeCurrent, newColorCurrent, _] = updateReversalSignal(
         currentExtremeBullishReversal or currentExtremeBearishReversal,
         currentVeryStrongBullishReversal or currentVeryStrongBearishReversal,
         currentStrongBullishReversal or currentStrongBearishReversal,
         currentStrongBullishReversal or currentVeryStrongBullishReversal or currentExtremeBullishReversal,
         lastReversalSignalCurrent, lastReversalTimeCurrent, lastReversalColorCurrent, 'Cur')
    [newSignalCurrent, newTimeCurrent, newColorCurrent]

[newSignalCurrent, newTimeCurrent, newColorCurrent] = updateReversalSignals()
lastReversalSignalCurrent := newSignalCurrent
lastReversalTimeCurrent   := newTimeCurrent
lastReversalColorCurrent  := newColorCurrent

f_getTrendText(score, dynThreshold, strongThreshold) =>
    switch
        score >= strongThreshold => '🚀 Strong Bullish'
        score >= (strongThreshold + dynThreshold) / 2 => '📈 MS Bullish'
        score >= dynThreshold => '📈 Moderate Bullish'
        score > 52 => '↗️ Weak Bullish'
        score >= 48 => '➡️ Neutral'
        score > 100 - dynThreshold => '↘️ Weak Bearish'
        score > (200 - strongThreshold - dynThreshold) / 2 => '📉 Moderate Bearish'
        score > 100 - strongThreshold => '📉 MS Bearish'
        => '💥 Strong Bearish'

f_getBarsAgo(lastBar, tf) =>
    barsAgo = bar_index - lastBar
    tf == 'Cur' ? str.tostring(barsAgo) + ' bars' :
     tf == 'D'   ? str.tostring(math.floor(barsAgo / 1440)) + ' days' :
     tf == 'W'   ? str.tostring(math.floor(barsAgo / 10080)) + ' weeks' : 'Unknown'

f_getTimeAgo(lastTime, tf) =>
    timeDiff = time - lastTime
    tf == 'Cur' ? str.tostring(math.floor(timeDiff / timeframe.multiplier)) + ' bars' :
     tf == 'D'   ? str.tostring(math.floor(timeDiff / (24 * 60 * 60 * 1000))) + ' days' :
     tf == 'W'   ? str.tostring(math.floor(timeDiff / (7 * 24 * 60 * 60 * 1000))) + ' weeks' : 'Unknown'

//────────────────────────────────────────────────────────────
// DASHBOARD SETUP & UPDATES (ENHANCED VISUALS)
//────────────────────────────────────────────────────────────
var table dashboardTable = table.new(position.top_right, 5, MAX_ROWS,
      bgcolor = color.new(color.rgb(27, 28, 36), 100 - dashboardOpacity),
      frame_width = 1, frame_color = color.rgb(55, 65, 81),
      border_width = 2, border_color = color.rgb(42, 46, 57))

// Generate a colored strength bar based on the provided value
f_generateStrengthBar(strengthValue, textSize, bullColor, bearColor, neutralColor) =>
    // Choose color based on strength value
    barColor = strengthValue > 6 ? bullColor :
               strengthValue < 4 ? bearColor :
               neutralColor

    // Create gradient strength bar
    strengthBar = ""
    filledCount = math.round(strengthValue)
    emptyCount = 10 - filledCount

    // Use different characters based on dashboard mode
    string filledChar = dashboardMode == "Compact" ? "█" : "■"
    string emptyChar = dashboardMode == "Compact" ? "░" : "□"

    for i = 1 to filledCount
        strengthBar := strengthBar + filledChar

    for i = 1 to emptyCount
        strengthBar := strengthBar + emptyChar

    [strengthBar, barColor]

// Generate an enhanced trend meter visualization
f_generateTrendMeter(score, dynThreshold, strongThreshold, bullColor, bearColor, neutralColor) =>
    if not showTrendMeter
        ["", color.new(color.white, 100)]
    else
        // Map score to a position on the meter (0-100)
        float meterPosition = score

        // Create a visual meter with markers for thresholds
        string meterBar = ""
        color meterColor = neutralColor

        // Determine the number of segments in the meter
        int totalSegments = 20
        int filledSegments = math.round(meterPosition / 100 * totalSegments)

        // Calculate threshold positions on the meter
        int neutralLowPos = math.round(48 / 100 * totalSegments)
        int neutralHighPos = math.round(52 / 100 * totalSegments)
        int dynThresholdLowPos = math.round((100 - dynThreshold) / 100 * totalSegments)
        int dynThresholdHighPos = math.round(dynThreshold / 100 * totalSegments)
        int strongThresholdLowPos = math.round((100 - strongThreshold) / 100 * totalSegments)
        int strongThresholdHighPos = math.round(strongThreshold / 100 * totalSegments)

        // Create the meter with threshold markers
        for i = 0 to totalSegments - 1
            if i == filledSegments
                // Add position marker
                meterBar := meterBar + "◆"
            else if i == neutralLowPos or i == neutralHighPos
                // Add neutral zone markers
                meterBar := meterBar + "│"
            else if i == dynThresholdLowPos or i == dynThresholdHighPos
                // Add dynamic threshold markers
                meterBar := meterBar + "┃"
            else if i == strongThresholdLowPos or i == strongThresholdHighPos
                // Add strong threshold markers
                meterBar := meterBar + "┣"
            else
                // Add regular segment
                meterBar := meterBar + "─"

        // Determine meter color based on position
        if meterPosition >= strongThreshold
            meterColor := bullColor
        else if meterPosition >= dynThreshold
            meterColor := color.new(bullColor, 40)
        else if meterPosition <= (100 - strongThreshold)
            meterColor := bearColor
        else if meterPosition <= (100 - dynThreshold)
            meterColor := color.new(bearColor, 40)
        else
            meterColor := neutralColor

        [meterBar, meterColor]

// Helper function to pad numbers to two digits
f_pad2(val) =>
    val_str = str.tostring(val)
    val < 10 ? "0" + val_str : val_str

// Main dashboard update function
f_updateDashboard() =>
    if showDashboard
        // Set dashboard position from input
        dashPos = switch dashboardPosition
            'Top Left'     => position.top_left
            'Top Right'    => position.top_right
            'Bottom Left'  => position.bottom_left
            'Bottom Right' => position.bottom_right

        // Determine text size from dashboardTextSize input
        textSize = f_getTextSize(dashboardTextSize)

        // Get theme colors
        [bgColor, frameColor, borderColor, titleBgColor, titleTextColor,
         headerBgColor, headerTextColor, rowBgColor, rowTextColor,
         bullColor, bearColor, neutralColor] = f_getDashboardColors()

        // Update dashboard table properties
        table.set_position(dashboardTable, dashPos)
        table.set_bgcolor(dashboardTable, bgColor)
        table.set_frame_color(dashboardTable, frameColor)
        table.set_border_color(dashboardTable, borderColor)

        // Determine number of columns based on dashboard mode
        int numColumns = dashboardMode == "Compact" ? 3 : 5

        // Title Row with theme-based styling
        string titleText = dashboardMode == "Compact" ? "Brandon Algo" : DASHBOARD_TITLE
        table.cell(dashboardTable, 0, TITLE_ROW, titleText,
                 bgcolor = titleBgColor,
                 text_color = titleTextColor,
                 text_size = textSize,
                 text_halign = text.align_center)
        table.merge_cells(dashboardTable, 0, TITLE_ROW, numColumns - 1, TITLE_ROW)

        // Header Row with theme-based styling
        array<string> headers = dashboardMode == "Compact" ?
                              array.from("TF", "TREND", "STR") :
                              array.from("TF", "TREND", "STRENGTH", "COND", "LAST REV")

        for i = 0 to math.min(array.size(headers) - 1, numColumns - 1)
            table.cell(dashboardTable, i, HEADER_ROW, array.get(headers, i),
                 bgcolor = headerBgColor,
                 text_color = headerTextColor,
                 text_size = textSize,
                 text_halign = text.align_center)

        // Data Row for the Current Timeframe with theme-based styling
        dataRow = HEADER_ROW + 1

        // Timeframe cell with subtle highlight and feature indicators
        string adaptiveInfo = ""
        string mlInfo = useML ? "ML" : ""
        string rlInfo = useRL ? "RL" : ""

        string featureInfo = ""
        if dashboardMode != "Compact"
            featureInfo := adaptiveInfo
            featureInfo := featureInfo + (featureInfo != "" and mlInfo != "" ? "+" : "") + mlInfo
            featureInfo := featureInfo + (featureInfo != "" and rlInfo != "" ? "+" : "") + rlInfo
            featureInfo := featureInfo != "" ? " (" + featureInfo + ")" : ""

        string tfText = dashboardMode == "Compact" ? "Cur" : "Current" + featureInfo

        table.cell(dashboardTable, 0, dataRow, tfText,
                 bgcolor = rowBgColor,
                 text_color = rowTextColor,
                 text_size = textSize,
                 text_halign = text.align_center)

        // Trend cell with better icons and styling
        string bullishIcon = "▲"  // Cleaner triangle
        string bearishIcon = "▼"  // Cleaner triangle
        string neutralIcon = "◆"  // Diamond for neutral

        string trendIcon = currentStrength == "Bullish" ? bullishIcon :
                          currentStrength == "Bearish" ? bearishIcon :
                          neutralIcon

        // Apply color emphasis based on trend
        color trendCellColor = currentStrength == "Bullish" ? color.new(bullColor, 80) :
                              currentStrength == "Bearish" ? color.new(bearColor, 80) :
                              rowBgColor

        // Enhanced trend text with confluence and momentum data
        string trendText = ""
        if dashboardMode == "Compact"
            trendText := trendIcon
        else if dashboardMode == "Detailed"
            // Add confluence and momentum information in detailed mode
            string confluenceText = "C:" + str.tostring(math.round(confluenceScore * 100)) + "%"
            string qualityText = "Q:" + str.tostring(math.round(trendQuality * 100)) + "%"
            string momentumText = "M:" + momentumStrength
            string confirmText = confirmedTrend ? "✓" : "?"
            trendText := trendIcon + " " + currentStrength + "\n" + confluenceText + " " + qualityText + "\n" + momentumText + " " + confirmText
        else
            trendText := trendIcon + " " + currentStrength

        table.cell(dashboardTable, 1, dataRow, trendText,
                 bgcolor = trendCellColor,
                 text_color = trendColor,
                 text_size = textSize,
                 text_halign = text.align_center)

        // Strength cell with enhanced bar visualization
        int strengthVal = math.round(normalizedTrendScore)

        if dashboardMode == "Detailed" and showTrendMeter
            // Use enhanced trend meter in detailed mode
            [meterStr, meterColor] = f_generateTrendMeter(normalizedTrendScore, dynamicThreshold, strongThreshold,
                                                         bullColor, bearColor, neutralColor)

            table.cell(dashboardTable, 2, dataRow, meterStr,
                     bgcolor = rowBgColor,
                     text_color = meterColor,
                     text_size = textSize,
                     text_halign = text.align_left)
        else
            // Use standard strength bar
            [barStr, barColor] = f_generateStrengthBar(strengthVal / 10, textSize, bullColor, bearColor, neutralColor)

            string strengthText = dashboardMode == "Compact" ? barStr : barStr + " " + str.tostring(strengthVal)

            table.cell(dashboardTable, 2, dataRow, strengthText,
                     bgcolor = rowBgColor,
                     text_color = barColor,
                     text_size = textSize,
                     text_halign = text.align_left)

        // Only show condition and last reversal in Standard or Detailed modes
        if dashboardMode != "Compact" and numColumns > 3
            // Condition cell with improved icons
            string trendingIcon = "⟫"  // More distinctive trending icon
            string rangingIcon = "⇄"   // More distinctive ranging icon

            // Define a local market condition based on directional efficiency
            string localMarketCondition = currentDirEfficiency > 0.55 ? "TRENDING" : "RANGING"
            string condIcon = localMarketCondition == "TRENDING" ? trendingIcon : rangingIcon
            color condColor = localMarketCondition == "TRENDING" ? color.rgb(120, 220, 255) : color.rgb(255, 215, 120)

            table.cell(dashboardTable, 3, dataRow, condIcon,
                     bgcolor = rowBgColor,
                     text_color = condColor,
                     text_size = textSize,
                     text_halign = text.align_center)

            // Last Reversal cell with improved styling
            string lastRevText = "-"
            color lastRevColor = rowTextColor  // Default color

            // Add tooltip information if enabled
            if showTooltips
                string tooltip = "Trend Score: " + str.tostring(normalizedTrendScore, "#.#") + "\n" +
                               "Dynamic Threshold: " + str.tostring(dynamicThreshold, "#.#") + "\n" +
                               "Strong Threshold: " + str.tostring(strongThreshold, "#.#") + "\n" +
                               "Market Regime: " + localMarketCondition + "\n" +
                               "Market Phase: " + currentMarketPhase + "\n" +
                               "Directional Efficiency: " + str.tostring(currentDirEfficiency, "#.##")

                if mtfEnabled
                    tooltip := tooltip + "\nMTF Alignment: " + str.tostring(currentMtfAlignmentScore, "#.#") + "%"

                if useVolumeAnalysis
                    tooltip := tooltip + "\nVolume Score: " + str.tostring(currentVolumeScore, "#.##")

                if useSwingPoints
                    tooltip := tooltip + "\nSwing Trend: " + currentSwingTrend +
                               "\nSwing Score: " + str.tostring(currentSwingScore, "#.##")

                // Add tooltip to strength cell
                table.cell_set_tooltip(dashboardTable, 2, dataRow, tooltip)

            // Process last reversal signal if in Standard or Detailed mode
            if lastReversalSignalCurrent != "None"
                if str.contains(lastReversalSignalCurrent, "Very Strong Bullish")
                    lastRevText := "▲▲ " + str.tostring(bar_index - lastReversalTimeCurrent)
                    lastRevColor := bullColor  // Use theme color
                else if str.contains(lastReversalSignalCurrent, "Strong Bullish")
                    lastRevText := "▲ " + str.tostring(bar_index - lastReversalTimeCurrent)
                    lastRevColor := color.new(bullColor, 30)  // Lighter bull color
                else if str.contains(lastReversalSignalCurrent, "Very Strong Bearish")
                    lastRevText := "▼▼ " + str.tostring(bar_index - lastReversalTimeCurrent)
                    lastRevColor := bearColor  // Use theme color
                else if str.contains(lastReversalSignalCurrent, "Strong Bearish")
                    lastRevText := "▼ " + str.tostring(bar_index - lastReversalTimeCurrent)
                    lastRevColor := color.new(bearColor, 30)  // Lighter bear color

            table.cell(dashboardTable, 4, dataRow, lastRevText,
                     bgcolor = rowBgColor,
                     text_color = lastRevColor,
                     text_size = textSize,
                     text_halign = text.align_center)

            // Add tooltip to last reversal cell if enabled
            if showTooltips
                string revTooltip = "Last Reversal: " + lastReversalSignalCurrent + "\n" +
                                   "Bars Since: " + str.tostring(bar_index - lastReversalTimeCurrent) + "\n" +
                                   "Time: " + str.format("{0}:{1}", f_pad2(hour(time)), f_pad2(minute(time)))
                table.cell_set_tooltip(dashboardTable, 4, dataRow, revTooltip)

        // RSI Row with improved styling - only show in Detailed mode or when debug is enabled
        if (debugMode or dashboardMode == "Detailed")
            int rsiRow = dataRow + 1
            color debugRowColor = color.new(color.rgb(40, 44, 52), 100 - dashboardOpacity)

            float rsiValue = currentRsi
            color rsiTextColor = rsiValue > 70 ? bullColor :
                               rsiValue < 30 ? bearColor :
                               rowTextColor

            // Compact mode shows less information
            if dashboardMode == "Compact"
                table.cell(dashboardTable, 0, rsiRow, "RSI:" + str.tostring(rsiValue, "#"),
                         bgcolor = debugRowColor,
                         text_color = rsiTextColor,
                         text_size = textSize,
                         text_halign = text.align_center)

                // Show StochRSI in compact form
                color stochColor = currentStochK > 80 ? bullColor :
                                 currentStochK < 20 ? bearColor :
                                 rowTextColor

                table.cell(dashboardTable, 1, rsiRow, "ST:" + str.tostring(currentStochK, "#"),
                         bgcolor = debugRowColor,
                         text_color = stochColor,
                         text_size = textSize,
                         text_halign = text.align_center)

                // Show DE in compact form
                color deColor = currentDirEfficiency > 0.7 ? bullColor :
                              currentDirEfficiency < 0.3 ? bearColor :
                              rowTextColor

                table.cell(dashboardTable, 2, rsiRow, "DE:" + str.tostring(currentDirEfficiency, "#.#"),
                         bgcolor = debugRowColor,
                         text_color = deColor,
                         text_size = textSize,
                         text_halign = text.align_center)
            else
                // Standard or Detailed mode
                table.cell(dashboardTable, 0, rsiRow, "RSI: " + str.tostring(rsiValue, "#.##"),
                         bgcolor = debugRowColor,
                         text_color = rsiTextColor,
                         text_size = textSize,
                         text_halign = text.align_center)
                table.merge_cells(dashboardTable, 0, rsiRow, 2, rsiRow)

                color stochColor = currentStochK > 80 ? bullColor :
                                 currentStochK < 20 ? bearColor :
                                 rowTextColor

                table.cell(dashboardTable, 3, rsiRow, "StochRSI: " + str.tostring(currentStochK, "#.##"),
                         bgcolor = debugRowColor,
                         text_color = stochColor,
                         text_size = textSize,
                         text_halign = text.align_center)
                table.merge_cells(dashboardTable, 3, rsiRow, 4, rsiRow)

                // Add tooltips if enabled
                if showTooltips
                    string rsiTooltip = "RSI: " + str.tostring(rsiValue, "#.##") + "\n" +
                                      "StochRSI K: " + str.tostring(currentStochK, "#.##") + "\n" +
                                      "StochRSI D: " + str.tostring(currentStochD, "#.##")
                    table.cell_set_tooltip(dashboardTable, 0, rsiRow, rsiTooltip)

            // Add new features row
            int newFeaturesRow = rsiRow + 1
            if dashboardMode == "Detailed" and (mtfEnabled or useVolumeAnalysis or useSwingPoints)
                // Display new features information
                string featuresText = ""
                if mtfEnabled
                    featuresText := "MTF: " + str.tostring(math.round(currentMtfAlignmentScore)) + "%"

                table.cell(dashboardTable, 0, newFeaturesRow, featuresText,
                         bgcolor = debugRowColor,
                         text_color = currentMtfAlignmentScore > mtfAlignmentThreshold ? bullColor : neutralColor,
                         text_size = textSize,
                         text_halign = text.align_center)

                if useVolumeAnalysis
                    string volText = "Vol: " + str.tostring(math.round(currentVolumeScore * 10) / 10, "#.#")
                    color volColor = currentVolumeScore > 1 ? bullColor :
                                     currentVolumeScore < -1 ? bearColor : neutralColor

                    table.cell(dashboardTable, 1, newFeaturesRow, volText,
                             bgcolor = debugRowColor,
                             text_color = volColor,
                             text_size = textSize,
                             text_halign = text.align_center)

                if useSwingPoints
                    string swingText = "Swing: " + (currentSwingTrend == "UPTREND" ? "↗" :
                                                  currentSwingTrend == "DOWNTREND" ? "↘" :
                                                  currentSwingTrend == "ACCUMULATION" ? "→" :
                                                  currentSwingTrend == "DISTRIBUTION" ? "←" : "•")
                    color swingColor = currentSwingTrend == "UPTREND" ? bullColor :
                                      currentSwingTrend == "DOWNTREND" ? bearColor : neutralColor

                    table.cell(dashboardTable, 2, newFeaturesRow, swingText,
                             bgcolor = debugRowColor,
                             text_color = swingColor,
                             text_size = textSize,
                             text_halign = text.align_center)



            // No adaptive parameters row

            // Add ML information row if ML is enabled
            int mlRow = 0
            if useML and showMLInfo
                if dashboardMode == "Detailed" and (mtfEnabled or useVolumeAnalysis or useSwingPoints)
                    mlRow := newFeaturesRow + 1
                else
                    mlRow := rsiRow + 1

                // Display ML weights and accuracy
                table.cell(dashboardTable, 0, mlRow, "ML Acc: " + str.tostring(g_mlAccuracy, "#.#") + "%",
                         bgcolor = debugRowColor,
                         text_color = color.rgb(120, 200, 255),
                         text_size = textSize,
                         text_halign = text.align_center)

                table.cell(dashboardTable, 1, mlRow, "M:" + str.tostring(array.get(mlWeights, 0), "#.##"),
                         bgcolor = debugRowColor,
                         text_color = color.rgb(120, 200, 255),
                         text_size = textSize,
                         text_halign = text.align_center)

                table.cell(dashboardTable, 2, mlRow, "R:" + str.tostring(array.get(mlWeights, 1), "#.##"),
                         bgcolor = debugRowColor,
                         text_color = color.rgb(120, 200, 255),
                         text_size = textSize,
                         text_halign = text.align_center)

                table.cell(dashboardTable, 3, mlRow, "D:" + str.tostring(array.get(mlWeights, 2), "#.##"),
                         bgcolor = debugRowColor,
                         text_color = color.rgb(120, 200, 255),
                         text_size = textSize,
                         text_halign = text.align_center)

            // Add RL information row if RL is enabled
            if useRL and showRLInfo
                int rlRow = 0
                if useML and showMLInfo
                    rlRow := mlRow + 1
                else if dashboardMode == "Detailed" and (mtfEnabled or useVolumeAnalysis or useSwingPoints)
                    rlRow := newFeaturesRow + 1
                else
                    rlRow := rsiRow + 1

                // Display RL parameters and rewards
                // Add harmonization indicator
                string harmonyIcon = g_rlHarmonizationScore > 0.8 ? "🔄" :
                                     g_rlHarmonizationScore > 0.5 ? "⚙️" : "⚠️"

                table.cell(dashboardTable, 0, rlRow, harmonyIcon + " RL: " + str.tostring(g_rlCumulativeReward, "#.#"),
                         bgcolor = debugRowColor,
                         text_color = color.rgb(255, 180, 130),
                         text_size = textSize,
                         text_halign = text.align_center)

                table.cell(dashboardTable, 1, rlRow, "VI:" + str.tostring(g_rlOptimizedVolatilityImpact, "#.##"),
                         bgcolor = debugRowColor,
                         text_color = color.rgb(255, 180, 130),
                         text_size = textSize,
                         text_halign = text.align_center)



                table.cell(dashboardTable, 3, rlRow, "DW:" + str.tostring(g_rlOptimizedDynamicWeightStrength, "#.##"),
                         bgcolor = debugRowColor,
                         text_color = color.rgb(255, 180, 130),
                         text_size = textSize,
                         text_halign = text.align_center)

                table.cell(dashboardTable, 4, rlRow, "MI:" + str.tostring(g_rlOptimizedMlWeightInfluence, "#.##"),
                         bgcolor = debugRowColor,
                         text_color = color.rgb(255, 180, 130),
                         text_size = textSize,
                         text_halign = text.align_center)

                // Add harmonization score row
                int harmonyRow = rlRow + 1

                // Calculate color based on harmonization score
                color harmonyColor = g_rlHarmonizationScore > 0.8 ? color.rgb(0, 200, 100) :
                                     g_rlHarmonizationScore > 0.5 ? color.rgb(200, 200, 0) :
                                     color.rgb(255, 100, 100)

                table.cell(dashboardTable, 0, harmonyRow, "Parameter Harmony:",
                         bgcolor = debugRowColor,
                         text_color = color.rgb(220, 220, 220),
                         text_size = textSize,
                         text_halign = text.align_right)

                // Create a visual harmony meter
                string harmonyMeter = ""
                int meterLength = 10
                int filledBars = math.round(g_rlHarmonizationScore * meterLength)

                for i = 1 to filledBars
                    harmonyMeter := harmonyMeter + "■"
                for i = filledBars + 1 to meterLength
                    harmonyMeter := harmonyMeter + "□"

                table.cell(dashboardTable, 1, harmonyRow, harmonyMeter + " " + str.tostring(g_rlHarmonizationScore * 100, "#.#") + "%",
                         bgcolor = debugRowColor,
                         text_color = harmonyColor,
                         text_size = textSize,
                         text_halign = text.align_left)
                table.merge_cells(dashboardTable, 1, harmonyRow, 4, harmonyRow)

// We'll move this code to a separate function that will be called after Perfect Trail calculations

// Function to generate a visual strength meter for Perfect Trail
f_generateTrailStrengthMeter(float strength, color bullColor, color bearColor) =>
    // Normalize strength to 0-10 range
    float normalizedStrength = math.min(10, math.max(0, math.abs(strength) * 2))
    int filledBars = math.round(normalizedStrength)

    // Create visual meter
    string meter = ""
    string filledChar = "■"
    string emptyChar = "□"

    // Add direction indicator
    string directionIndicator = strength > 0 ? "↗ " : "↘ "

    // Fill the meter based on strength
    for i = 1 to filledBars
        meter := meter + filledChar

    for i = filledBars + 1 to 10
        meter := meter + emptyChar

    // Determine color based on direction and strength
    color meterColor = strength > 0 ?
                      color.from_gradient(normalizedStrength, 0, 10, color.new(bullColor, 70), bullColor) :
                      color.from_gradient(normalizedStrength, 0, 10, color.new(bearColor, 70), bearColor)

    [directionIndicator + meter, meterColor]

// Function to update Perfect Trail information in the dashboard with enhanced visuals
f_updatePerfectTrailDashboard() =>
    if showDashboard and dashboardMode == "Detailed" and showPerfectTrail
        // Get theme colors
        [bgColor, frameColor, borderColor, titleBgColor, titleTextColor,
         headerBgColor, headerTextColor, rowBgColor, rowTextColor,
         bullColor, bearColor, neutralColor] = f_getDashboardColors()

        // Determine text size from dashboardTextSize input
        textSize = f_getTextSize(dashboardTextSize)

        // Determine row position - use a fixed approach
        int trailRow = HEADER_ROW + 2  // Start after the header row

        // Add rows based on which features are enabled
        if debugMode
            trailRow += 1  // Add one for RSI row



        if useML and showMLInfo
            trailRow += 1  // Add one for ML row

        if useRL and showRLInfo
            trailRow += 2  // Add two for RL row and harmony row

        // Display Perfect Trail status with enhanced visuals
        string trailTrendIcon = g_perfectTrailTrend ? "▲" : "▼"

        // Add visual indicator for trend change
        if g_perfectTrailTrendChange
            trailTrendIcon := g_perfectTrailTrend ? "⬆" : "⬇"

        // Get theme-appropriate colors
        color bullTrailColor = dashboardColorTheme == 'Default' ? perfectTrailBullColor :
                              dashboardColorTheme == 'Monochrome' ? color.white :
                              dashboardColorTheme == 'High Contrast' ? color.lime :
                              dashboardColorTheme == 'Pastel' ? color.rgb(130, 200, 180) :
                              perfectTrailBullColor

        color bearTrailColor = dashboardColorTheme == 'Default' ? perfectTrailBearColor :
                              dashboardColorTheme == 'Monochrome' ? color.gray :
                              dashboardColorTheme == 'High Contrast' ? color.fuchsia :
                              dashboardColorTheme == 'Pastel' ? color.rgb(220, 150, 170) :
                              perfectTrailBearColor

        color trailTrendColor = g_perfectTrailTrend ? bullTrailColor : bearTrailColor

        // Highlight color for trend changes
        color highlightColor = g_perfectTrailTrendChange ?
                              color.new(g_perfectTrailTrend ? bullTrailColor : bearTrailColor, 80) :
                              rowBgColor

        table.cell(dashboardTable, 0, trailRow, "Perfect Trail:",
                 bgcolor = highlightColor,
                 text_color = color.rgb(220, 220, 220),
                 text_size = textSize,
                 text_halign = text.align_right)

        // Enhanced status display with visual indicator
        string statusText = trailTrendIcon + " " + (g_perfectTrailTrend ? "Bullish" : "Bearish")

        // Add signal indicator if present
        if g_perfectTrailSignal
            statusText := statusText + " ⚡"

        table.cell(dashboardTable, 1, trailRow, statusText,
                 bgcolor = highlightColor,
                 text_color = trailTrendColor,
                 text_size = textSize,
                 text_halign = text.align_left)

        // Show parameters with improved styling
        table.cell(dashboardTable, 2, trailRow, "MA:" + str.tostring(perfectTrailMALength),
                     bgcolor = highlightColor,
                     text_color = color.rgb(180, 180, 220),
                     text_size = textSize,
                     text_halign = text.align_center)

        table.cell(dashboardTable, 3, trailRow, "ATR:" + str.tostring(perfectTrailATRLength),
                     bgcolor = highlightColor,
                     text_color = color.rgb(180, 180, 220),
                     text_size = textSize,
                     text_halign = text.align_center)

        // Show trend strength with visual indicator
        string strengthText = "Str: " + str.tostring(math.abs(g_perfectTrailStrength), "#.#")
        table.cell(dashboardTable, 4, trailRow, strengthText,
                     bgcolor = highlightColor,
                     text_color = trailTrendColor,
                     text_size = textSize,
                     text_halign = text.align_center)

        // Add strength meter row
        int strengthRow = trailRow + 1

        // Generate strength meter
        [meterStr, meterColor] = f_generateTrailStrengthMeter(
             g_perfectTrailTrend ? g_perfectTrailStrength : -g_perfectTrailStrength,
             bullTrailColor,
             bearTrailColor
             )

        table.cell(dashboardTable, 0, strengthRow, "Trail Strength:",
                 bgcolor = rowBgColor,
                 text_color = color.rgb(220, 220, 220),
                 text_size = textSize,
                 text_halign = text.align_right)

        table.cell(dashboardTable, 1, strengthRow, meterStr,
                 bgcolor = rowBgColor,
                 text_color = meterColor,
                 text_size = textSize,
                 text_halign = text.align_left)
        table.merge_cells(dashboardTable, 1, strengthRow, 4, strengthRow)

        // Add tooltip with enhanced information
        if showTooltips
            string trailTooltip = "Perfect Trail Status: " + (g_perfectTrailTrend ? "Bullish" : "Bearish") + "\n" +
                               "Trend Strength: " + str.tostring(math.abs(g_perfectTrailStrength), "#.##") + "\n" +
                               "Signal Present: " + (g_perfectTrailSignal ? "Yes (" + g_perfectTrailSignalType + ")" : "No") + "\n" +
                               "Trend Changed: " + (g_perfectTrailTrendChange ? "Yes" : "No") + "\n"

            trailTooltip := trailTooltip +
                             "MA Length: " + str.tostring(perfectTrailMALength) + "\n" +
                             "ATR Length: " + str.tostring(perfectTrailATRLength) + "\n" +
                             "Bands Distance: " + str.tostring(perfectTrailBandsDist, "#.##") + "\n" +
                             "Confirmation Bars: " + str.tostring(perfectTrailConfirmBars) + "\n" +
                             "MA Type: " + perfectTrailMAType

            table.cell_set_tooltip(dashboardTable, 1, trailRow, trailTooltip)

// Update the dashboard on every bar
f_updateDashboard()
// Update Perfect Trail dashboard information
f_updatePerfectTrailDashboard()

//────────────────────────────────────────────────────────────
// SMALL ALERTS & PLOT SETTINGS
//────────────────────────────────────────────────────────────
trendColor := f_getColor(normalizedTrendScore, dynamicThreshold, strongThreshold)
barcolor(trendColor)

significantTrendChange = math.abs(ta.change(normalizedTrendScore)) > 10
trendChangeBullish = ta.crossover(normalizedTrendScore, strongThreshold)
trendChangeBearish = ta.crossunder(normalizedTrendScore, 100 - strongThreshold)
plotarrow(debugMode ? (trendChangeBullish ? 1 : trendChangeBearish ? -1 : 0) : 0,
          title="Trend Change Arrow",
          colorup=color.green,
          colordown=color.red,
          maxheight=15)

// Plot swing points if enabled
var bool plotSwingHigh = false
var bool plotSwingLow = false
plotSwingHigh := useSwingPoints and currentIsSwingHigh
plotSwingLow := useSwingPoints and currentIsSwingLow
plotshape(plotSwingHigh ? high : na, title="Swing High", location=location.abovebar,
          color=color.new(color.red, 0), style=shape.triangledown, size=size.tiny)
plotshape(plotSwingLow ? low : na, title="Swing Low", location=location.belowbar,
          color=color.new(color.green, 0), style=shape.triangleup, size=size.tiny)

// Plot volume divergences if enabled
var bool plotBullishVolDiv = false
var bool plotBearishVolDiv = false
plotBullishVolDiv := useVolumeAnalysis and currentBullishVolDiv
plotBearishVolDiv := useVolumeAnalysis and currentBearishVolDiv
plotshape(plotBullishVolDiv ? low : na, title="Bullish Volume Divergence",
          location=location.belowbar, color=color.new(color.green, 0), style=shape.diamond, size=size.small)
plotshape(plotBearishVolDiv ? high : na, title="Bearish Volume Divergence",
          location=location.abovebar, color=color.new(color.red, 0), style=shape.diamond, size=size.small)

// Add market phase label if enabled
var label marketPhaseLabel = na
if barstate.islast
    label.delete(marketPhaseLabel)  // Just delete the label without creating a new one

// Add multi-timeframe alignment indicator if enabled
var bool showMtfAlignment = false
showMtfAlignment := mtfEnabled and debugMode
plot(showMtfAlignment ? currentMtfAlignmentScore : na, "MTF Alignment", color=color.new(color.purple, 0), linewidth=2)

// Add neutral alignment reference line (must be outside conditional)
var float mtfAlignmentLine = na
mtfAlignmentLine := showMtfAlignment ? 50 : na
plot(mtfAlignmentLine, "Neutral MTF Alignment", color=color.new(color.gray, 70), style=plot.style_line)

// Plot reversal signals
plotshape(currentStrongBullishReversal ? low : na,
          title="Strong Bullish Reversal",
          location=location.belowbar,
          color=color.new(color.yellow, 0),
          style=shape.triangleup,
          size=size.tiny)
plotshape(currentStrongBearishReversal ? high : na,
          title="Strong Bearish Reversal",
          location=location.abovebar,
          color=color.new(color.yellow, 0),
          style=shape.triangledown,
          size=size.tiny)
plotshape(currentVeryStrongBullishReversal ? high : na,
          title="Very Strong Bullish Reversal",
          location=location.abovebar,
          color=color.new(color.blue, 0),
          style=shape.triangledown,
          size=size.tiny)
plotshape(currentVeryStrongBearishReversal ? low : na,
          title="Very Strong Bearish Reversal",
          location=location.belowbar,
          color=color.new(color.blue, 0),
          style=shape.triangleup,
          size=size.tiny)
plotshape(currentExtremeBullishReversal ? high : na,
          title="Extreme Bullish Reversal",
          location=location.abovebar,
          color=color.new(color.purple, 0),
          style=shape.cross,
          size=size.small)
plotshape(currentExtremeBearishReversal ? low : na,
          title="Extreme Bearish Reversal",
          location=location.belowbar,
          color=color.new(color.purple, 0),
          style=shape.cross,
          size=size.small)

//────────────────────────────────────────────────────────────
// ALERT MESSAGE CONSTRUCTION & TRIGGERING
//────────────────────────────────────────────────────────────
generateAlertMessage() =>
    string message = ""
    if alertStrongReversal
        if currentStrongBullishReversal
            message := message + "🟡 Yellow: Strong Bullish Reversal - Possible upward move\n"
        if currentStrongBearishReversal
            message := message + "🟡 Yellow: Strong Bearish Reversal - Possible downward move\n"
    if alertVeryStrongReversal
        if currentVeryStrongBullishReversal
            message := message + "🔵 Blue: Very Strong Bullish Reversal - Likely upward trend\n"
        if currentVeryStrongBearishReversal
            message := message + "🔵 Blue: Very Strong Bearish Reversal - Likely downward trend\n"
    if alertExtremeReversal
        if currentExtremeBullishReversal
            message := message + "🟣 Purple: Extreme Bullish Reversal - Potential sharp drop\n"
        if currentExtremeBearishReversal
            message := message + "🟣 Purple: Extreme Bearish Reversal - Potential sharp rise\n"
    if alertTrendStart
        if ta.crossover(normalizedTrendScore, strongThreshold)
            message := message + "🟢 Strong Bullish Trend Starting\n"
        if ta.crossover(normalizedTrendScore, dynamicThreshold)
            message := message + "🟢 Weak Bullish Trend Starting\n"
        if ta.crossunder(normalizedTrendScore, 100 - dynamicThreshold)
            message := message + "🔴 Weak Bearish Trend Starting\n"
        if ta.crossunder(normalizedTrendScore, 100 - strongThreshold)
            message := message + "🔴 Strong Bearish Trend Starting\n"
    if alertSignificantChange and significantTrendChange
        message := message + "⚠️ Significant Trend Change Detected\n"

    // Add Perfect Trail signals
    if showPerfectTrail
        if g_perfectTrailSignal and g_perfectTrailSignalType == "bullish"
            message := message + "🟩 Perfect Trail: Bullish Signal - Potential uptrend\n"
        if g_perfectTrailSignal and g_perfectTrailSignalType == "bearish"
            message := message + "🟥 Perfect Trail: Bearish Signal - Potential downtrend\n"
        if g_perfectTrailTrendChange
            message := message + "🔄 Perfect Trail: Trend Change to " + (g_perfectTrailTrend ? "Bullish" : "Bearish") + "\n"

    message

alertMessage = generateAlertMessage()
if alertMessage != ""
    alert(alertMessage, alert.freq_once_per_bar_close)

// Enhanced alert conditions with confluence and momentum filtering
alertcondition(alertTrendStart and ta.crossover(normalizedTrendScore, strongThreshold) and confirmedTrend and trendQuality > 0.4,
          title="High Quality Strong Bullish Trend",
          message="High Quality Strong Bullish trend detected with confirmed confluence and momentum")

alertcondition(alertTrendStart and ta.crossover(normalizedTrendScore, dynamicThreshold) and confluenceScore > 0.3,
          title="Confirmed Bullish Trend Start",
          message="Confirmed Bullish trend detected with good confluence")

alertcondition(alertTrendStart and ta.crossunder(normalizedTrendScore, 100 - dynamicThreshold) and confluenceScore > 0.3,
          title="Confirmed Bearish Trend Start",
          message="Confirmed Bearish trend detected with good confluence")

alertcondition(alertTrendStart and ta.crossunder(normalizedTrendScore, 100 - strongThreshold) and confirmedTrend and trendQuality > 0.4,
          title="High Quality Strong Bearish Trend",
          message="High Quality Strong Bearish trend detected with confirmed confluence and momentum")

// Additional high-confidence alerts for momentum changes
alertcondition(alertTrendStart and confirmedTrend and momentumStrength == "Strong" and trendQuality > 0.5,
          title="High Momentum Trend Signal",
          message="High momentum trend signal detected with strong quality metrics")
alertcondition(alertSignificantChange and significantTrendChange,
          title="Significant Trend Change",
          message="Significant change in trend detected")
alertcondition(alertStrongReversal and currentStrongBullishReversal,
          title="Strong Bullish Reversal",
          message="Strong Bullish Reversal detected")
alertcondition(alertStrongReversal and currentStrongBearishReversal,
          title="Strong Bearish Reversal",
          message="Strong Bearish Reversal detected")
alertcondition(alertVeryStrongReversal and currentVeryStrongBullishReversal,
          title="Very Strong Bullish Reversal",
          message="Very Strong Bullish Reversal detected")
alertcondition(alertVeryStrongReversal and currentVeryStrongBearishReversal,
          title="Very Strong Bearish Reversal",
          message="Very Strong Bearish Reversal detected")
alertcondition(alertExtremeReversal and currentExtremeBullishReversal,
          title="Extreme Bullish Reversal",
          message="Extreme Bullish Reversal detected")
alertcondition(alertExtremeReversal and currentExtremeBearishReversal,
          title="Extreme Bearish Reversal",
          message="Extreme Bearish Reversal detected")

// Perfect Trail alert conditions with enhanced messages
alertcondition(showPerfectTrail and g_perfectTrailSignal and g_perfectTrailSignalType == "bullish",
          title="Perfect Trail Bullish Signal",
          message="Perfect Trail Bullish Signal detected - Potential uptrend forming")

alertcondition(showPerfectTrail and g_perfectTrailSignal and g_perfectTrailSignalType == "bearish",
          title="Perfect Trail Bearish Signal",
          message="Perfect Trail Bearish Signal detected - Potential downtrend forming")


// Global variables for trail signal confirmation
var int g_trailSignalConfirmed = 0
var float g_lastSignalStrength = 0.0
var color g_lastSignalColor = color.white

// Update trail signal confirmation with enhanced visual tracking
if showPerfectTrail and showTrailSignals
    if g_perfectTrailSignal
        g_trailSignalConfirmed := trailSignalConfirmation
        g_lastSignalStrength := math.abs(g_perfectTrailStrength)
        g_lastSignalColor := g_perfectTrailSignalType == "bullish" ? perfectTrailBullColor : perfectTrailBearColor
    else
        g_trailSignalConfirmed := math.max(0, g_trailSignalConfirmed - 1)

// Plot enhanced signal indicators when signals are confirmed
plotshape(showPerfectTrail and showTrailSignals and g_trailSignalConfirmed > 0 and g_perfectTrailSignalType == "bullish",
         title="Enhanced Trail Bullish Signal",
         style=shape.labelup,
         location=location.belowbar,
         color=color.new(perfectTrailBullColor, 0),
         textcolor=color.white,
         text="BUY",
         size=size.small)

plotshape(showPerfectTrail and showTrailSignals and g_trailSignalConfirmed > 0 and g_perfectTrailSignalType == "bearish",
         title="Enhanced Trail Bearish Signal",
         style=shape.labeldown,
         location=location.abovebar,
         color=color.new(perfectTrailBearColor, 0),
         textcolor=color.white,
         text="SELL",
         size=size.small)

//────────────────────────────────────────────────────────────
// PERFECT TRAIL V3 CALCULATIONS
//────────────────────────────────────────────────────────────

// Using fixed parameters for Perfect Trail (no adaptive parameters)

// MA calculation based on selected type
// Always use fixed parameters for stability
float perfectTrailMA = 0.0

// Calculate MA with fixed length - this is the original, stable implementation
if perfectTrailMAType == 'wma'
    perfectTrailMA := ta.wma(close, perfectTrailMALength)
else if perfectTrailMAType == 'sma'
    perfectTrailMA := ta.sma(close, perfectTrailMALength)
else if perfectTrailMAType == 'ema'
    perfectTrailMA := ta.ema(close, perfectTrailMALength)
else if perfectTrailMAType == 'rma'
    perfectTrailMA := ta.rma(close, perfectTrailMALength)
else if perfectTrailMAType == 'vwma'
    perfectTrailMA := ta.vwma(close, perfectTrailMALength)
else
    perfectTrailMA := ta.ema(close, perfectTrailMALength)

// Enhanced trend calculation function
perfectTrailTrend(source) =>
    // Always use fixed parameters for stability
    float atr = ta.atr(perfectTrailATRLength)
    float bandsDist = perfectTrailBandsDist

    // Calculate bands using the appropriate bands distance
    float upper = source + atr * bandsDist
    float lower = source - atr * bandsDist
    float band1 = 0.
    float band2 = 0.
    float band3 = 0.
    var bool trend = false
    var float trendStrength = 0.0
    var int trendConfirmation = 0
    var bool trailSignal = false
    var string signalType = "none"

    float maSlope = source - source[1]
    bool upperCross = ta.crossover(close, upper)
    bool lowerCross = ta.crossunder(close, lower)

    // Volume confirmation if available
    float volFactor = 1.0
    if volume > 0  // Check if volume data is available
        float avgVol = ta.sma(volume, 20)
        volFactor := volume / math.max(avgVol, 0.0001)
        volFactor := math.min(2.0, math.max(0.5, volFactor))  // Limit impact

    // Enhanced trend strength calculation with volume
    trendStrength := trendStrength * 0.9
    if upperCross
        trendStrength += 1 * volFactor
    else if lowerCross
        trendStrength -= 1 * volFactor

    trendConfirmation := int(math.sign(trendStrength))

    // MA Slope confirmation with momentum
    bool bullishSlope = maSlope > 0
    bool bearishSlope = maSlope < 0

    // Add momentum confirmation
    float momentum = ta.rsi(source, 14) - 50  // Normalized momentum (-50 to +50)
    bullishSlope := bullishSlope and momentum > 0
    bearishSlope := bearishSlope and momentum < 0

    // Trend determination with enhanced confirmation
    bool prevTrend = trend

    // Use fixed confirmation bars for stability
    if trendConfirmation >= perfectTrailConfirmBars and bullishSlope
        trend := true
    else if trendConfirmation <= -perfectTrailConfirmBars and bearishSlope
        trend := false

    // Generate trail signals
    trailSignal := false
    signalType := "none"

    // For boolean variables, we can't use na() directly
    // Instead, we check if the trend has changed and we're not on the first calculation
    if trend != prevTrend and bar_index > 0
        trailSignal := true
        signalType := trend ? "bullish" : "bearish"

    // Calculate bands with dynamic multipliers
    float band2Mult = perfectTrailBand2Mult
    float band3Mult = perfectTrailBand3Mult

    // Adjust multipliers based on trend strength
    float strengthAdjustment = math.min(1.0, math.abs(trendStrength) / 5) * 0.3
    if trend
        // In bullish trend, widen upper bands
        band2Mult := perfectTrailBand2Mult * (1 + strengthAdjustment)
        band3Mult := perfectTrailBand3Mult * (1 + strengthAdjustment)
    else
        // In bearish trend, widen lower bands
        band2Mult := perfectTrailBand2Mult * (1 + strengthAdjustment)
        band3Mult := perfectTrailBand3Mult * (1 + strengthAdjustment)

    band1 := trend ? lower : upper
    band2 := trend ? lower + atr * band2Mult : upper - atr * band2Mult
    band3 := trend ? lower + atr * band3Mult : upper - atr * band3Mult

    [band1, band2, band3, trend, atr, trendStrength, trailSignal, signalType]

// Get trail values with enhanced calculation
[band1, band2, band3, localTrend, atr, localTrendStrength, localTrailSignal, localSignalType] = perfectTrailTrend(perfectTrailMA)

// FIX: Use 'barstate.isfirst' to handle the initialization on the first bar.
// This is the correct and standard Pine Script way to avoid the 'na' checking paradox on non-numerical series.
// If it's the first bar, the previous trend is initialized to the current trend. Otherwise, we safely use the historical value.
previous_perfectTrailTrend = barstate.isfirst ? localTrend : g_perfectTrailTrend[1]

// 1. Now, compare the new localTrend with the valid previous state.
g_perfectTrailTrendChange := localTrend != previous_perfectTrailTrend

// 2. Then, update the global variables for the current bar's state.
g_perfectTrailTrend := localTrend
g_perfectTrailStrength := localTrendStrength
g_perfectTrailSignal := localTrailSignal
g_perfectTrailSignalType := localSignalType

// Dynamic transparency based on trend strength and confidence
float strength_factor = math.min(math.abs(g_perfectTrailStrength) / 3, 1)
float transparency = perfectTrailBandOpacity * (1 - strength_factor)

// Enhanced color determination with theme support
color bullTrailColor = dashboardColorTheme == 'Default' ? perfectTrailBullColor :
                      dashboardColorTheme == 'Monochrome' ? color.white :
                      dashboardColorTheme == 'High Contrast' ? color.lime :
                      dashboardColorTheme == 'Pastel' ? color.rgb(130, 200, 180) :
                      perfectTrailBullColor

color bearTrailColor = dashboardColorTheme == 'Default' ? perfectTrailBearColor :
                      dashboardColorTheme == 'Monochrome' ? color.gray :
                      dashboardColorTheme == 'High Contrast' ? color.fuchsia :
                      dashboardColorTheme == 'Pastel' ? color.rgb(220, 150, 170) :
                      perfectTrailBearColor

// Enhanced color determination with advanced visual improvements
// Calculate a more dynamic transparency based on trend strength with improved scaling
float dynamicTransparency = math.max(5, perfectTrailBandOpacity - math.abs(g_perfectTrailStrength) * 15)

// Create pulsing effect for visual emphasis - more subtle for established trends, stronger for new trends
float pulseEffect = g_perfectTrailTrendChange ?
                   math.abs(math.sin(bar_index * 0.8) * 40) : // Faster pulse on trend change
                   math.abs(math.sin(bar_index * 0.3) * 15)   // Slower, subtler pulse for established trend

// Create rich gradient colors for trail bands with enhanced visual distinction
// Base colors with improved saturation for better visual impact
color baseBullColor = color.from_gradient(math.abs(g_perfectTrailStrength), 0, 5,
                     color.rgb(31, 160, 117), // Original bullish color
                     color.rgb(41, 200, 147)) // Brighter variant for stronger trends

color baseBearColor = color.from_gradient(math.abs(g_perfectTrailStrength), 0, 5,
                     color.rgb(173, 37, 48),  // Original bearish color
                     color.rgb(213, 47, 68))  // Brighter variant for stronger trends

// Create more visually distinct trail colors
color trailColor = g_perfectTrailTrendChange ? color(na) :
                  g_perfectTrailTrend ? baseBullColor : baseBearColor

// Create brighter edge colors for better definition
color trailColorBright = g_perfectTrailTrendChange ? color(na) :
                         g_perfectTrailTrend ?
                         color.from_gradient(g_perfectTrailStrength, 0, 5, baseBullColor, color.new(baseBullColor, 0)) :
                         color.from_gradient(-g_perfectTrailStrength, 0, 5, baseBearColor, color.new(baseBearColor, 0))

// Create highlight effect for trend changes with improved visibility
color highlightColor = g_perfectTrailTrendChange ?
                       (g_perfectTrailTrend ? color.new(color.white, 30) : color.new(color.white, 30)) :
                       color(na)

// Calculate fill colors with enhanced visual properties
// Primary fill color with trend-strength based saturation
color fillColorPrimary = g_perfectTrailTrend ?
                         color.from_gradient(math.abs(g_perfectTrailStrength), 0, 5,
                                           color.new(baseBullColor, 70),
                                           color.new(baseBullColor, 30)) :
                         color.from_gradient(math.abs(g_perfectTrailStrength), 0, 5,
                                           color.new(baseBearColor, 70),
                                           color.new(baseBearColor, 30))

// Secondary fill color for gradient effect
color fillColorSecondary = g_perfectTrailTrend ?
                          color.from_gradient(math.abs(g_perfectTrailStrength), 0, 5,
                                             color.new(baseBullColor, 85),
                                             color.new(baseBullColor, 60)) :
                          color.from_gradient(math.abs(g_perfectTrailStrength), 0, 5,
                                             color.new(baseBearColor, 85),
                                             color.new(baseBearColor, 60))

// Plot the bands with improved styling and visual effects
// Use fixed plot styles with enhanced visual properties
p1 = plot(showPerfectTrail ? band1 : na, 'Trail Band 1',
     color = g_perfectTrailTrendChange ? highlightColor : trailColorBright,
     linewidth = 3,
     style = plot.style_line)

p2 = plot(showPerfectTrail ? band2 : na, 'Trail Band 2',
     color = g_perfectTrailTrendChange ? color(na) : g_perfectTrailTrend ?
             color.new(baseBullColor, 25) : color.new(baseBearColor, 25),
     linewidth = 2)

// Create a third plot for gradient fill effect (not visible as a line)
p3 = plot(showPerfectTrail ? (band1 + band2) / 2 : na, 'Trail Gradient Midpoint',
     color = color(na),  // Invisible line
     linewidth = 1)

// Enhanced fill between bands with multi-gradient styling for more visual depth
// First gradient from band1 to midpoint
fill(p1, p3, fillColorPrimary, 'Trail Bands Fill Primary')

// Second gradient from midpoint to band2 for a richer visual effect
fill(p3, p2, fillColorSecondary, 'Trail Bands Fill Secondary')

// Add visual indicators for trend changes
plotshape(showPerfectTrail and g_perfectTrailTrendChange and g_perfectTrailTrend ? band1 : na,
         title="Trail Bullish Change",
         style=shape.diamond,
         location=location.absolute,
         color=color.new(bullTrailColor, 0),
         size=size.small)

plotshape(showPerfectTrail and g_perfectTrailTrendChange and not g_perfectTrailTrend ? band1 : na,
         title="Trail Bearish Change",
         style=shape.diamond,
         location=location.absolute,
         color=color.new(bearTrailColor, 0),
         size=size.small)

// Add highlight circles for trend changes
plotshape(showPerfectTrail and g_perfectTrailTrendChange ? band1 : na,
         title="Trail Change Highlight",
         style=shape.circle,
         location=location.absolute,
         color=color.new(color.white, 50),
         size=size.small)