//@version=5
// ====================================================================
// BrandonJames - S/R Pro v4.6
// ====================================================================

indicator("BrandonJames - S/R Pro v4.6.7", shorttitle="[BrandonJames] S/R Pro v4.6.7", overlay=true, max_bars_back=5000)

// ====================================================================
// INPUT PARAMETERS - ORGANIZED BY FUNCTIONALITY
// ====================================================================

// ----- Core Settings -----
group_core = "⚙️ Core Settings"
max_blocks  = input.int(7, "📦 Max Blocks Displayed", minval=1, maxval=20, group=group_core, tooltip="Maximum number of order blocks displayed on chart")
analysis_blocks = input.int(15, "🔍 Analysis Blocks", minval=5, maxval=50, group=group_core, tooltip="Number of blocks to keep in memory for analysis (higher values improve detection quality)")
star_selection = input.string("⭐⭐⭐", "⭐ Min Star Rating", options=["⭐", "⭐⭐", "⭐⭐⭐", "⭐⭐⭐⭐", "⭐⭐⭐⭐⭐"], group=group_core, tooltip="Filter orderblocks by minimum star rating (1-5 stars)")
min_star_rating = switch star_selection
    "⭐" => 1
    "⭐⭐" => 2
    "⭐⭐⭐" => 3
    "⭐⭐⭐⭐" => 4
    "⭐⭐⭐⭐⭐" => 5
extendObs   = input.int(10, "➡️ Extend Right", minval=1, maxval=50, group=group_core, tooltip="Extend orderblocks to right after price")
debugMode   = input.bool(false, "  Debug Mode", group=group_core)

// ----- Lookback Settings -----
group_lookback = "🔍 Lookback Settings"
lookbackPeriod     = input.int(6, "🎯 Lookback Period", minval=2, group=group_lookback, tooltip="Number of bars for pivot calculation")
dynamicATRPeriod   = input.int(14, "ATR Period", group=group_lookback, tooltip="ATR period used for calculations")

// ----- Lookback Optimization -----
group_lookback_opt = "🔧 Lookback Optimization"
enableLookbackOptimization = input.bool(false, "🚀 Enable Lookback Optimization", group=group_lookback_opt, tooltip="Automatically find the best lookback period based on orderblock strength")
minLookbackRange = input.int(5, "📉 Min Lookback Range", minval=2, maxval=15, group=group_lookback_opt, tooltip="Minimum lookback period to test")
maxLookbackRange = input.int(20, "📈 Max Lookback Range", minval=10, maxval=50, group=group_lookback_opt, tooltip="Maximum lookback period to test")
maxBlocksPerLookback = input.int(5, "📦 Max Blocks Per Test", minval=3, maxval=10, group=group_lookback_opt, tooltip="Maximum orderblocks to consider per lookback period")
showOptimizedValue = input.bool(true, "📊 Show Optimized Value", group=group_lookback_opt, tooltip="Display which lookback value was selected")

// ----- Volume Settings -----
group_volume         = "📊 Volume Settings"
volMultiplier        = input.float(1.2, "📉 Threshold", minval=0.1, step=0.1, group=group_volume, tooltip="Volume significance multiplier")
buySellDominanceThreshold = input.float(60.0, "🔵🔴 Buy/Sell Dominance %", minval=50, maxval=100, step=1, group=group_volume, tooltip="Threshold to determine buy/sell dominance")
vol_len              = input.int(2, "📈 Smoothing", minval=1, group=group_volume, tooltip="Bars for volume filtering")
volumeSpikeFactor    = input.float(2.0, "🚀 Volume Spike Factor", minval=1.5, maxval=5.0, step=0.1, group=group_volume, tooltip="Minimum volume spike required at pivot formation")
checkVolumeSpike     = input.bool(false, "✅ Require Volume Spike", group=group_volume, tooltip="Only create order blocks when pivot forms with significant volume")
volumeSpikeWindow    = input.int(3, "📊 Volume Spike Window", minval=1, maxval=10, group=group_volume, tooltip="Number of bars to check for volume spike around pivot")

// ----- Liquidity Pool Detection -----
group_liquidity = "💧 Liquidity Pool Detection"
enableLiquidityPools = input.bool(false, "💧 Enable Liquidity Pools", group=group_liquidity, tooltip="Detect areas where stop losses likely cluster")
liquidityLookback = input.int(50, "🔍 Liquidity Lookback", minval=20, maxval=200, group=group_liquidity, tooltip="Bars to look back for liquidity pools")
liquidityMinAge = input.int(10, "⏰ Min Pool Age", minval=5, maxval=50, group=group_liquidity, tooltip="Minimum bars since high/low for valid liquidity")
liquidityStrengthBonus = input.float(2.0, "💪 Strength Bonus", minval=1.0, maxval=5.0, step=0.5, group=group_liquidity, tooltip="Bonus multiplier for liquidity pool zones")
liquidityRetestCount = input.int(2, "🔄 Min Retests", minval=0, maxval=5, group=group_liquidity, tooltip="Minimum times level should be tested")
showLiquidityMarkers = input.bool(true, "🎯 Show Liquidity Markers", group=group_liquidity, tooltip="Display markers for liquidity levels")

// Enhanced liquidity visualization options
liquidityStyle = input.string("Zones", "💧 Liquidity Style", options=["Markers", "Zones", "Lines", "Heatmap"], group=group_liquidity, tooltip="Choose how to display liquidity areas")
showLiquiditySweeps = input.bool(true, "🚀 Show Liquidity Sweeps", group=group_liquidity, tooltip="Alert when liquidity levels are swept")
liquidityMinStrength = input.float(2.0, "💪 Min Strength", minval=1.0, maxval=5.0, step=0.5, group=group_liquidity, tooltip="Minimum strength to display liquidity level")
maxLiquidityLevels = input.int(5, "📊 Max Levels", minval=3, maxval=10, group=group_liquidity, tooltip="Maximum liquidity levels to track")
heatmapBars = input.int(100, "📊 Heatmap Bars", minval=50, maxval=200, group=group_liquidity, tooltip="Number of bars for heatmap calculation")
liquidityExtension = input.int(25, "➡️ Extend Liquidity", minval=10, maxval=50, group=group_liquidity, tooltip="How far to extend liquidity lines/zones")

// Enhanced Lines & Zones Options
lineStyle = input.string("Dashed", "📏 Line Style", options=["Solid", "Dashed", "Dotted"], group=group_liquidity, tooltip="Style for liquidity lines")
lineWidth = input.int(2, "📏 Line Width", minval=1, maxval=5, group=group_liquidity, tooltip="Width of liquidity lines")
strongLineWidth = input.int(3, "💪 Strong Line Width", minval=2, maxval=6, group=group_liquidity, tooltip="Width for high-strength liquidity lines")
zoneHeight = input.float(0.15, "📊 Zone Height", minval=0.05, maxval=0.5, step=0.05, group=group_liquidity, tooltip="Height multiplier for liquidity zones (ATR based)")
strongZoneHeight = input.float(0.25, "💪 Strong Zone Height", minval=0.1, maxval=0.8, step=0.05, group=group_liquidity, tooltip="Height for high-strength zones")
zoneTransparency = input.int(80, "🎨 Zone Transparency", minval=50, maxval=95, group=group_liquidity, tooltip="Transparency level for zones")
strongZoneTransparency = input.int(70, "💪 Strong Zone Transparency", minval=40, maxval=90, group=group_liquidity, tooltip="Transparency for high-strength zones")
showZoneBorders = input.bool(true, "🔲 Show Zone Borders", group=group_liquidity, tooltip="Display borders around zones")
zoneBorderWidth = input.int(1, "🔲 Border Width", minval=1, maxval=3, group=group_liquidity, tooltip="Width of zone borders")
enhancedColors = input.bool(true, "🌈 Enhanced Colors", group=group_liquidity, tooltip="Use gradient colors based on strength")
showStrengthLabels = input.bool(true, "💪 Show Strength Labels", group=group_liquidity, tooltip="Display strength values on lines/zones")

// Color Customization
highLiquidityColor = input.color(color.red, "🔴 High Liquidity Color", group=group_liquidity, tooltip="Base color for resistance/high liquidity levels")
lowLiquidityColor = input.color(color.green, "🟢 Low Liquidity Color", group=group_liquidity, tooltip="Base color for support/low liquidity levels")
strongLiquidityColor = input.color(color.purple, "🟣 Strong Liquidity Color", group=group_liquidity, tooltip="Color for very strong liquidity levels")
weakLiquidityColor = input.color(color.gray, "⚪ Weak Liquidity Color", group=group_liquidity, tooltip="Color for weak liquidity levels")
showLiquidityDirection = input.bool(true, "➡️ Show Direction Arrows", group=group_liquidity, tooltip="Display directional arrows on liquidity levels")
animateNewLevels = input.bool(true, "✨ Animate New Levels", group=group_liquidity, tooltip="Subtle animation for newly created levels")

// Advanced Visual Options
linePattern = input.string("Standard", "📏 Line Pattern", options=["Standard", "Double", "Triple"], group=group_liquidity, tooltip="Pattern style for liquidity lines")
zonePattern = input.string("Solid", "📊 Zone Pattern", options=["Solid", "Gradient", "Striped"], group=group_liquidity, tooltip="Fill pattern for liquidity zones")
showLevelPrice = input.bool(true, "💰 Show Level Price", group=group_liquidity, tooltip="Display price value on liquidity levels")
priceFormat = input.string("Auto", "💰 Price Format", options=["Auto", "2 Decimals", "4 Decimals", "Full"], group=group_liquidity, tooltip="Price display format")
showTimeStamp = input.bool(false, "⏰ Show Time Stamp", group=group_liquidity, tooltip="Display creation time on levels")
levelOpacity = input.int(85, "🎨 Level Opacity", minval=10, maxval=100, group=group_liquidity, tooltip="Overall opacity for all liquidity levels")

// Enhanced Liquidity Analysis
enableSmartClustering = input.bool(true, "🧠 Smart Clustering", group=group_liquidity, tooltip="Automatically cluster nearby liquidity levels to reduce noise")
clusteringThreshold = input.float(0.5, "📏 Clustering Threshold %", minval=0.1, maxval=2.0, step=0.1, group=group_liquidity, tooltip="Price distance threshold for clustering levels (% of ATR)")
enhancedStrengthCalc = input.bool(true, "💪 Enhanced Strength", group=group_liquidity, tooltip="Use advanced multi-factor strength calculation")
volumeWeight = input.float(0.3, "📊 Volume Weight", minval=0.0, maxval=1.0, step=0.1, group=group_liquidity, tooltip="Weight of volume in strength calculation")
testFrequencyWeight = input.float(0.25, "🎯 Test Frequency Weight", minval=0.0, maxval=1.0, step=0.05, group=group_liquidity, tooltip="Weight of test frequency in strength calculation")
ageWeight = input.float(0.2, "⏰ Age Weight", minval=0.0, maxval=1.0, step=0.05, group=group_liquidity, tooltip="Weight of level age in strength calculation")
proximityWeight = input.float(0.15, "📍 Proximity Weight", minval=0.0, maxval=1.0, step=0.05, group=group_liquidity, tooltip="Weight of price proximity in strength calculation")
confluenceWeight = input.float(0.1, "🎯 Confluence Weight", minval=0.0, maxval=1.0, step=0.05, group=group_liquidity, tooltip="Weight of technical confluence in strength calculation")

// Validation Settings
validationEnabled = input.bool(true, "✅ Level Validation", group=group_liquidity, tooltip="Enable advanced validation for liquidity levels")
minVolumeThreshold = input.float(1.2, "📊 Min Volume Threshold", minval=0.5, maxval=3.0, step=0.1, group=group_liquidity, tooltip="Minimum volume ratio for valid levels")
minTestCount = input.int(1, "🎯 Min Test Count", minval=1, maxval=5, group=group_liquidity, tooltip="Minimum number of tests for level validation")
maxLevelAge = input.int(200, "⏰ Max Level Age", minval=50, maxval=500, step=10, group=group_liquidity, tooltip="Maximum age in bars for level validation")
priceActionValidation = input.bool(true, "📈 Price Action Validation", group=group_liquidity, tooltip="Validate levels based on price action quality")

// Advanced Sweep Detection
enhancedSweepDetection = input.bool(true, "🌊 Enhanced Sweep Detection", group=group_liquidity, tooltip="Use advanced algorithms for liquidity sweep detection")
sweepConfirmationBars = input.int(3, "⏱️ Sweep Confirmation", minval=1, maxval=10, group=group_liquidity, tooltip="Bars required to confirm a liquidity sweep")
sweepVolumeThreshold = input.float(1.5, "📊 Sweep Volume Threshold", minval=1.0, maxval=3.0, step=0.1, group=group_liquidity, tooltip="Minimum volume ratio for sweep confirmation")
showSweepLabels = input.bool(true, "🏷️ Show Sweep Labels", group=group_liquidity, tooltip="Display labels when liquidity sweeps occur")
sweepLabelStyle = input.string("Detailed", "🎨 Sweep Label Style", options=["Simple", "Detailed", "Minimal"], group=group_liquidity, tooltip="Style of sweep notification labels")

// Quality Metrics
showQualityMetrics = input.bool(true, "📊 Quality Metrics", group=group_liquidity, tooltip="Display quality scores for liquidity levels")
qualityThreshold = input.float(0.6, "⭐ Quality Threshold", minval=0.1, maxval=1.0, step=0.1, group=group_liquidity, tooltip="Minimum quality score for level display")
adaptiveQuality = input.bool(true, "🔄 Adaptive Quality", group=group_liquidity, tooltip="Automatically adjust quality thresholds based on market conditions")

// Heatmap display options
showHeatmapLabels = input.bool(true, "🏷️ Show Heatmap Labels", group=group_liquidity, tooltip="Display percentage labels on heatmap zones")
heatmapLabelThreshold = input.float(25.0, "📊 Label Threshold %", minval=0.0, maxval=100.0, step=5.0, group=group_liquidity, tooltip="Minimum intensity percentage to show labels")
showHeatmapLegend = input.bool(true, "📋 Show Heatmap Legend", group=group_liquidity, tooltip="Display color legend for heatmap")
heatmapZoneThreshold = input.float(20.0, "🎯 Zone Threshold %", minval=0.0, maxval=50.0, step=5.0, group=group_liquidity, tooltip="Minimum intensity percentage to display zones")
heatmapZoneCount = input.int(15, "📊 Zone Count", minval=10, maxval=25, group=group_liquidity, tooltip="Number of zones for heatmap granularity")

// ----- Order Flow Imbalance -----
group_orderflow = "🌊 Order Flow Imbalance"
enableOrderFlow = input.bool(false, "🌊 Enable Order Flow", group=group_orderflow, tooltip="Detect zones with significant buy/sell imbalances")
orderFlowPeriod = input.int(20, "📊 Analysis Period", minval=5, maxval=50, group=group_orderflow, tooltip="Bars to analyze for order flow")
orderFlowThreshold = input.float(70.0, "🎯 Imbalance Threshold %", minval=50.0, maxval=90.0, step=5.0, group=group_orderflow, tooltip="Minimum imbalance percentage to create zone")
deltaSmoothing = input.int(3, "📈 Delta Smoothing", minval=1, maxval=10, group=group_orderflow, tooltip="Smooth delta calculations")
orderFlowMinVolume = input.float(1.5, "📊 Min Volume Ratio", minval=1.0, maxval=3.0, step=0.1, group=group_orderflow, tooltip="Minimum volume vs average")
showDeltaDivergence = input.bool(true, "📈 Show Delta Divergence", group=group_orderflow, tooltip="Highlight price/delta divergences")

// ----- Block Appearance -----
group_block_appearance = "📏 Block Appearance"
showPriceLabels      = input.bool(true, "🏷️ Price Labels", group=group_block_appearance)
showMidline          = input.bool(true, "➖ Show Midlines", group=group_block_appearance)
useDynamicBox        = input.bool(true, "🔄 Dynamic Size", group=group_block_appearance, tooltip="Adjust width based on volatility")
adaptiveBoxSize      = input.bool(true, "📊 Adaptive Size", group=group_block_appearance, tooltip="Automatically adjust box size based on market conditions")
box_width            = input.float(1, "📏 Base Width", minval=0, step=0.1, group=group_block_appearance, tooltip="ATR multiplier for box size")
dynamicBoxMultiplier = input.float(1.0, "🌀 Multiplier", minval=0.1, step=0.1, group=group_block_appearance, tooltip="ATR multiplier for dynamic width")
minBoxSize           = input.float(0.5, "🔻 Min Size", minval=0.1, step=0.1, group=group_block_appearance, tooltip="Minimum box size as ATR multiplier")
maxBoxSize           = input.float(8.0, "🔺 Max Size", minval=1.0, step=0.5, group=group_block_appearance, tooltip="Maximum box size as ATR multiplier")
boxSizeVolatilityWeight = input.float(0.7, "📈 Volatility Weight", minval=0.0, maxval=1.0, step=0.1, group=group_block_appearance, tooltip="Weight of volatility in dynamic box size calculation")
midlineColor         = input.color(color.rgb(255,255,255,70), "Midline Color", group=group_block_appearance)

// ----- Block Behavior -----
group_block_behavior = "⚙️ Block Behavior"
hideOverlaps         = input.bool(true, "🚫 Hide Overlapping", group=group_block_behavior)
showBreakerBlocks    = input.bool(true, "💥 Show Broken", group=group_block_behavior)
showMitigated        = input.bool(false, "⚡ Show Mitigated", group=group_block_behavior)
useTrendFilter       = input.bool(false, "📈 Use Trend Filter (EMA200)", group=group_block_behavior, tooltip="Only create blocks if trend conditions are met: supports allowed when price is above EMA200, resistances when below EMA200")

// ----- Block Colors -----
group_block_colors = "🎨 Block Colors"
bullColor        = input.color(color.rgb(0,180,108,75), "🟢 Bullish Fill", group=group_block_colors)
bearColor        = input.color(color.rgb(255,82,82,75), "🔴 Bearish Fill", group=group_block_colors)
breakoutColor    = input.color(color.rgb(255,215,0,75), "💥 Broken Fill", group=group_block_colors)
mitigatedColor   = input.color(color.rgb(128,128,128,85), "⚡ Mitigated Fill", group=group_block_colors)

// Enhanced Border Colors
bullBorderColor  = input.color(color.rgb(0,220,128), "🟢 Bullish Border", group=group_block_colors)
bearBorderColor  = input.color(color.rgb(255,100,100), "🔴 Bearish Border", group=group_block_colors)
brokenBorderColor = input.color(color.rgb(255,165,0), "💥 Broken Border", group=group_block_colors)
mitigatedBorderColor = input.color(color.rgb(160,160,160), "⚡ Mitigated Border", group=group_block_colors)

// Advanced Color Options
enhancedColorMode = input.bool(true, "Enhanced Color Mode", group=group_block_colors, tooltip="Use advanced color gradients and effects")
strengthBasedColors = input.bool(true, "Strength-Based Colors", group=group_block_colors, tooltip="Adjust color intensity based on zone strength")
volumeBasedBorders = input.bool(true, "Volume-Based Borders", group=group_block_colors, tooltip="Thicker borders for high-volume zones")

// ----- Visual Enhancements -----
group_visual            = "🎨 Visual Enhancements"

// Theme Presets
themePreset = input.string("Professional", "🎨 Theme Preset", options=["Professional", "Dark", "Neon", "Minimal", "Colorblind", "Custom"], group=group_visual, tooltip="Pre-configured color themes")

// Enhanced Block Styling
blockStyle = input.string("Standard", "📦 Block Style", options=["Standard", "Gradient", "3D Shadow", "Rounded", "Glow"], group=group_visual, tooltip="Visual style for order blocks")
gradientIntensity = input.float(0.3, "🌈 Gradient Intensity", minval=0.0, maxval=1.0, step=0.1, group=group_visual, tooltip="Strength of gradient effect")
shadowOffset = input.int(2, "🔳 Shadow Offset", minval=0, maxval=10, group=group_visual, tooltip="Shadow distance for 3D effect")
glowEffect = input.bool(true, "✨ Glow Effect", group=group_visual, tooltip="Add subtle glow around important blocks")

// Smart Label System
smartLabels = input.bool(true, "🧠 Smart Labels", group=group_visual, tooltip="Intelligent label positioning to avoid overlaps")
iconLabels = input.bool(true, "🔣 Icon Labels", group=group_visual, tooltip="Use icons instead of text for cleaner look")
multiLineTooltips = input.bool(true, "📝 Rich Tooltips", group=group_visual, tooltip="Detailed hover information")
labelAnimation = input.bool(true, "🎬 Label Animation", group=group_visual, tooltip="Subtle animations for new labels")

// Display Options
displayModeOpt          = input.string("Compact", "Display Mode", options=["Compact", "Detailed", "Minimal", "Full"], group=group_visual, tooltip="Information density level")
orderBlockTextSizeOpt   = input.string("normal", "Block Text Size", options=["tiny", "small", "normal", "large"], group=group_visual)
orderBlockTextSize      = orderBlockTextSizeOpt == "tiny" ? size.tiny : orderBlockTextSizeOpt == "small" ? size.small : orderBlockTextSizeOpt == "normal" ? size.normal : size.large
orderBlockBorderStyleOpt = input.string("solid", "Border Style", options=["solid", "dashed", "dotted"], group=group_visual)
orderBlockBorderStyle   = orderBlockBorderStyleOpt == "solid" ? line.style_solid : orderBlockBorderStyleOpt == "dashed" ? line.style_dashed : line.style_dotted
orderBlockBorderWidth   = input.int(1, "Border Width", minval=1, group=group_visual)

// Advanced Visual Effects
applyBlockAgeFading     = input.bool(true, "⏰ Age Fading", group=group_visual, tooltip="Older blocks become more transparent")
pulseNewBlocks = input.bool(true, "💓 Pulse New Blocks", group=group_visual, tooltip="Subtle pulsing effect for newly created blocks")
highlightActiveBlocks = input.bool(true, "🎯 Highlight Active", group=group_visual, tooltip="Special highlighting for blocks being tested")
showTrendArrows = input.bool(true, "➡️ Trend Arrows", group=group_visual, tooltip="Directional indicators on blocks")

// Layout and Positioning
labelOffset             = input.int(5, "Label Offset", group=group_visual, tooltip="Vertical offset for price labels to reduce overlap")
smartSpacing = input.bool(true, "📏 Smart Spacing", group=group_visual, tooltip="Automatic spacing adjustment based on chart zoom")
midlineLineWidth        = input.int(1, "Midline Width", minval=1, group=group_visual)
tableTextSizeOpt        = input.string("small", "Table Text Size", options=["tiny", "small", "normal", "large"], group=group_visual, tooltip="Text size for all tables")
tableTextSize           = tableTextSizeOpt == "tiny" ? size.tiny : tableTextSizeOpt == "small" ? size.small : tableTextSizeOpt == "normal" ? size.normal : size.large

// Interactive Features
showPerformanceMetrics = input.bool(true, "📊 Performance Panel", group=group_visual, tooltip="Live performance tracking panel")
showMarketRegimeIndicator = input.bool(true, "🌡️ Market Regime", group=group_visual, tooltip="Visual market condition indicator")
showPerformancePanel = input.bool(true, "📊 Performance Panel", group=group_visual, tooltip="Show performance statistics")
showMarketRegime = input.bool(true, "📈 Market Regime", group=group_visual, tooltip="Display market regime indicator")

// ----- Smart Filtering -----
group_smart = "🧠 Smart Filtering"
useSmartFiltering = input.bool(true, "Enable Smart Filtering", group=group_smart, tooltip="Filter out low-quality zones based on multiple criteria")
showZoneStrength = input.bool(true, "Show Zone Strength", group=group_smart, tooltip="Display strength rating for each zone")
minZoneStrength = input.float(1.2, "Min Zone Strength", minval=0.5, step=0.1, group=group_smart, tooltip="Minimum strength multiplier for a zone to be displayed")
useVolumeProfile = input.bool(true, "Use Volume Profile", group=group_smart, tooltip="Consider volume profile when evaluating zone strength")
minVolumeRatio = input.float(1.5, "Min Volume Ratio", minval=1.0, step=0.1, group=group_smart, tooltip="Minimum ratio of zone volume to average volume")
dynamicStarRating = input.bool(true, "Dynamic Star Rating", group=group_smart, tooltip="Adjust star rating threshold based on market volatility")
volatilityAdjustment = input.float(0.8, "Volatility Adjustment", minval=0.1, maxval=2.0, step=0.1, group=group_smart, tooltip="Strength of volatility adjustment (higher values = stronger adjustment)")

// Weighted Confluence Settings
// Removed weight variables - using original sr old.pine scoring system

// ----- Confluence Detection -----
group_confluence = "🎯 Confluence Detection"
detectConfluence = input.bool(true, "Detect Confluence", group=group_confluence, tooltip="Highlight zones that align with other technical factors")
highlightConfluence = input.bool(true, "Highlight Confluence Zones", group=group_confluence, tooltip="Apply special highlighting to zones with confluence")
prioritizeConfluence = input.bool(true, "Prioritize Confluence", group=group_confluence, tooltip="Prioritize blocks with confluence factors when filtering")
confluenceBoost = input.float(1.5, "Confluence Boost", minval=1.0, maxval=3.0, step=0.1, group=group_confluence, tooltip="Boost strength score for blocks with confluence")
useRSI = input.bool(true, "Use RSI", group=group_confluence, tooltip="Consider RSI levels for confluence")
rsiPeriod = input.int(14, "RSI Period", minval=1, group=group_confluence)
rsiOverbought = input.int(70, "RSI Overbought", minval=50, maxval=100, group=group_confluence)
rsiOversold = input.int(30, "RSI Oversold", minval=0, maxval=50, group=group_confluence)
useMACD = input.bool(true, "Use MACD", group=group_confluence, tooltip="Consider MACD for confluence")
macdFastLength = input.int(12, "MACD Fast Length", minval=1, group=group_confluence)
macdSlowLength = input.int(26, "MACD Slow Length", minval=1, group=group_confluence)
macdSignalLength = input.int(9, "MACD Signal Length", minval=1, group=group_confluence)

// ----- Time Filtering -----
group_time_filter    = "⏰ Time Filtering"
enableTimeFilter     = input.bool(false, "Enable Time Filtering", group=group_time_filter, tooltip="Filter out older blocks based on age")
maxBlockAge          = input.int(100, "Max Block Age (bars)", minval=10, maxval=500, group=group_time_filter, tooltip="Maximum age in bars for blocks to be displayed")

// ----- Reversal Detection -----
group_reversal = "🔄 Reversal Detection"
showReversalTriangles = input.bool(true, "🔼🔽 Show Reversals", group=group_reversal)
bullishReversalColor  = input.color(color.rgb(255,80,0,60), "🟢 Bullish", group=group_reversal)
bearishReversalColor  = input.color(color.rgb(0,255,128,60), "🔴 Bearish", group=group_reversal)

// ----- Moving Averages -----
group_ma    = "📈 Moving Averages"
show_ema200    = input.bool(false, "� EMA 200", group=group_ma, tooltip="200-period Exponential Moving Average")
ema200_source  = input.source(close, "EMA200 Source", group=group_ma)
ema200_length  = input.int(200, "EMA200 Length", minval=1, group=group_ma)
ema200_color   = input.color(color.rgb(0,128,255,80), "EMA200 Color", group=group_ma)

show_ema50   = input.bool(false, "🟣 EMA 50", group=group_ma, tooltip="50-period Exponential Moving Average")
ema50_source = input.source(close, "EMA50 Source", group=group_ma)
ema50_length = input.int(50, "EMA50 Length", minval=1, group=group_ma)
ema50_color  = input.color(color.rgb(178,102,255,80), "EMA50 Color", group=group_ma)

show_sma    = input.bool(false, "� SMA", group=group_ma, tooltip="Simple Moving Average")
sma_source  = input.source(high, "SMA Source", group=group_ma)
sma_length  = input.int(25, "SMA Length", minval=1, group=group_ma)
sma_color   = input.color(color.rgb(255,215,0,80), "SMA Color", group=group_ma)

// ----- Bar Coloring Settings -----
group_bar_coloring = "🎨 Bar Coloring Settings"
enableBarColoring = input.bool(true, "🎨 Enable Bar Coloring", group=group_bar_coloring, tooltip="Enable enhanced bar coloring based on S/R levels")
barColoringMode = input.string("Zone Proximity", "🌈 Coloring Mode", options=["Zone Proximity", "Block Strength", "Liquidity Levels", "Combined"], group=group_bar_coloring, tooltip="Zone Proximity: color based on distance to S/R levels, Block Strength: color based on order block strength, Liquidity Levels: color based on liquidity zones, Combined: multi-factor coloring")
barColorTheme = input.string("Default", "🎭 Color Theme", options=["Default", "Monochrome", "High Contrast", "Pastel", "Custom"], group=group_bar_coloring, tooltip="Choose color theme for bar coloring")
barColorIntensity = input.float(1.0, "⚡ Color Intensity", minval=0.1, maxval=3.0, step=0.1, group=group_bar_coloring, tooltip="Intensity of bar coloring effects")
proximityThreshold = input.float(2.0, "📏 Proximity Threshold (ATR)", minval=0.5, maxval=5.0, step=0.1, group=group_bar_coloring, tooltip="Distance threshold for proximity-based coloring in ATR multiples")

// Custom Bar Colors (only used when theme is 'Custom')
customSupportColor = input.color(color.rgb(0, 255, 128), "🟢 Support Color", group=group_bar_coloring)
customResistanceColor = input.color(color.rgb(255, 100, 100), "🔴 Resistance Color", group=group_bar_coloring)
customStrongSupportColor = input.color(color.rgb(0, 200, 100), "🟢 Strong Support Color", group=group_bar_coloring)
customStrongResistanceColor = input.color(color.rgb(255, 50, 50), "🔴 Strong Resistance Color", group=group_bar_coloring)
customNeutralBarColor = input.color(color.rgb(200, 200, 200), "⚪ Neutral Color", group=group_bar_coloring)
customLiquidityColor = input.color(color.rgb(255, 215, 0), "💧 Liquidity Color", group=group_bar_coloring)

// ====================================================================
// UTILITY FUNCTIONS
// ====================================================================

// Cleanup function to remove all visual elements of an order block
// This reduces code duplication and improves maintainability
f_cleanupOrderBlock(ob) =>
    if not na(ob)
        box.delete(ob.box)
        if not na(ob.midline)
            line.delete(ob.midline)
        if not na(ob.top_price_label)
            label.delete(ob.top_price_label)
        if not na(ob.bottom_price_label)
            label.delete(ob.bottom_price_label)
        if not na(ob.confluence_label)
            label.delete(ob.confluence_label)

// ====================================================================
// FUNCTIONS FOR MARKET REGIME DETECTION
// ====================================================================

// Improved ADX calculation that uses the built-in function correctly
f_adx(length) =>
    // Get ADX directly from the third return value of ta.dmi
    [_, _, adx] = ta.dmi(length, length)
    adx

// Detect market regime (trending, ranging, or volatile)
// Returns: 0 = ranging, 1 = trending, 2 = volatile
f_detectMarketRegime() =>
    // Calculate ADX for trend strength
    float adx_value = f_adx(14)

    // Calculate volatility with zero-division protection
    float volatility = close != 0 ? (ta.atr(dynamicATRPeriod) / close * 100) : 0

    // Calculate price movement consistency
    int up_bars = 0
    int down_bars = 0
    for i = 0 to 9
        if close[i] > close[i+1]
            up_bars += 1
        else if close[i] < close[i+1]
            down_bars += 1

    float consistency = math.abs(up_bars - down_bars) / 10

    // Determine regime
    if adx_value > 30 and consistency > 0.6
        1  // Trending
    else if volatility > 2.5  // High volatility threshold (2.5% of price)
        2  // Volatile
    else
        0  // Ranging

// Get timeframe multiplier for lookback adaptation
f_getTimeframeMultiplier() =>
    float tf_minutes = timeframe.in_seconds() / 60

    if tf_minutes <= 5
        1.0  // Lower timeframes - use standard lookback
    else if tf_minutes <= 15
        1.2  // 15m
    else if tf_minutes <= 60
        1.5  // 1h
    else if tf_minutes <= 240
        2.0  // 4h
    else if tf_minutes <= 1440
        3.0  // Daily
    else
        4.0  // Weekly and above

// Global variables for caching
var int   cachedRegime = na
var int   lastRegimeUpdate = na
var float cachedATR = na

// Enhanced adaptive box size function
f_getAdaptiveBoxSize() =>
    // Calculate ATR internally
    float current_atr = ta.atr(dynamicATRPeriod)
    
    // Base calculation using the calculated ATR
    float base_size = current_atr * dynamicBoxMultiplier
    float size_mult = 1.0  // Default multiplier (neutral)

    // Extract function call for consistency
    int box_regime = f_detectMarketRegime()

    // Adjust based on regime
    if box_regime == 1  // Trending
        // Wider boxes in trending markets
        size_mult := 1.3
    else if box_regime == 2  // Volatile
        // Wider boxes in volatile markets
        size_mult := 1.5
    else  // Ranging
        // Narrower boxes in ranging markets
        size_mult := 0.8

    // Apply volatility weight with safety checks using the calculated ATR
    float atr_sma = ta.sma(current_atr, 50)
    float volatility_factor = atr_sma > 0 ? current_atr / atr_sma : 1.0
    float volatility_adjustment = 1.0 + math.max(-0.5, math.min(0.5, volatility_factor - 1.0)) * boxSizeVolatilityWeight

    // Calculate final size with bounds using the calculated ATR
    float final_size = base_size * size_mult * volatility_adjustment
    math.max(minBoxSize * current_atr, math.min(maxBoxSize * current_atr, final_size))

// ====================================================================
// ENHANCED VOLUME ANALYSIS FUNCTIONS
// ====================================================================

f_detectVolumeSpike(pivot_bar_offset) =>
    if not checkVolumeSpike
        true // Skip check if disabled
    else
        avg_volume = ta.sma(volume, 20)[pivot_bar_offset]
        
        bool spike_detected = false
        // Check the window starting from the pivot bar and looking towards the present
        for i = 0 to volumeSpikeWindow - 1
            // Ensure we don't look into the future
            int check_index = pivot_bar_offset - i
            if check_index >= 0
                if volume[check_index] >= avg_volume * volumeSpikeFactor
                    spike_detected := true
                    break
        
        spike_detected

// Enhanced volume profile analysis within block range
f_analyzeVolumeProfile(upper_level, lower_level, lookback_bars) =>
    float total_volume_in_range = 0.0
    float volume_near_edges = 0.0
    int bars_in_range = 0
    
    // Analyze volume distribution within the price range
    for i = 0 to math.min(lookback_bars, 50)  // Limit to prevent excessive computation
        float bar_high = high[i]
        float bar_low = low[i]
        float bar_volume = volume[i]
        
        // Check if bar intersects with our range
        if bar_high >= lower_level and bar_low <= upper_level
            total_volume_in_range += bar_volume
            bars_in_range += 1
            
            // Check if volume is concentrated near edges (top/bottom 25% of range)
            float range_size = upper_level - lower_level
            float edge_threshold = range_size * 0.25
            
            if (bar_high >= upper_level - edge_threshold) or (bar_low <= lower_level + edge_threshold)
                volume_near_edges += bar_volume
    
    // Calculate edge concentration ratio
    float edge_concentration = total_volume_in_range > 0 ? volume_near_edges / total_volume_in_range : 0.0
    
    [total_volume_in_range, edge_concentration, bars_in_range]

// ====================================================================
// FUNCTIONS FOR CONFLUENCE DETECTION AND ZONE STRENGTH
// ====================================================================

// Calculate RSI confluence
f_rsiConfluence(level, is_support) =>
    rsi_value = ta.rsi(close, rsiPeriod)
    if is_support
        rsi_value <= rsiOversold  // Oversold condition for support
    else
        rsi_value >= rsiOverbought  // Overbought condition for resistance

// Calculate MACD confluence
f_macdConfluence(level, is_support) =>
    [macdLine, signalLine, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
    if is_support
        macdLine < 0 and macdLine > signalLine  // Bullish crossover below zero
    else
        macdLine > 0 and macdLine < signalLine  // Bearish crossover above zero

// Calculate MA confluence
f_maConfluence(level, is_support) =>
    // Dynamic tolerance: larger of 1% or 0.5 ATR
    float tolPerc = level * 0.01
    float tolAtr = ta.atr(dynamicATRPeriod) * 0.5
    float tol = math.max(tolPerc, tolAtr)

    float ema50_val = ta.ema(ema50_source, ema50_length)
    float ema200_val = ta.ema(ema200_source, ema200_length)

    bool near_ema50 = math.abs(level - ema50_val) <= tol
    bool near_ema200 = math.abs(level - ema200_val) <= tol

    near_ema50 or near_ema200

// Calculate volume confluence
f_volumeConfluence(volume) =>
    avgVolume = ta.sma(volume, 20)
    volume >= avgVolume * minVolumeRatio

// f_calculateZoneStrength() - Original sr old.pine implementation for better accuracy
// Simplified scoring system that provides more reliable strength ratings
f_calculateZoneStrength(float vol_ratio, bool rsi_conf, bool macd_conf, bool ma_conf, bool vol_conf) =>
    // Base strength from volume
    float volumeStrength = math.min(vol_ratio / minVolumeRatio, 2.0) * 2.5  // 0-5 points from volume

    // Confluence bonus (original sr old.pine values)
    float confluenceBonus = 0.0
    int confluenceCount = 0

    if rsi_conf
        confluenceBonus += 1.0
        confluenceCount += 1
    if macd_conf
        confluenceBonus += 1.0
        confluenceCount += 1
    if ma_conf
        confluenceBonus += 1.5
        confluenceCount += 1
    if vol_conf
        confluenceBonus += 1.5
        confluenceCount += 1

    // Apply confluence boost if enabled and block has confluence
    if prioritizeConfluence and confluenceCount > 0
        float boost_factor = 1.0 + (confluenceCount / 4.0) * (confluenceBoost - 1.0)
        confluenceBonus *= boost_factor

    // Calculate total score (0-10 scale)
    float totalScore = math.min(volumeStrength + confluenceBonus, 10.0)
    totalScore

// Get color-coded strength rating text
// Original sr old.pine thresholds for consistent rating
f_getColorCodedStarRating(strength) =>
    if strength >= 8.0
        "🟢🟢🟢🟢🟢"  // Green for strongest
    else if strength >= 6.0
        "🟢🟢🟢🟢⚪"
    else if strength >= 4.0
        "🟡🟡🟡⚪⚪"  // Yellow for medium
    else if strength >= 2.0
        "🟠🟠⚪⚪⚪"  // Orange for weaker
    else
        "🔴⚪⚪⚪⚪"  // Red for weakest

// Cache star rating thresholds for better performance
// Original sr old.pine thresholds
var float[] base_star_thresholds = array.new_float(5, 0.0)
if barstate.isfirst
    array.set(base_star_thresholds, 0, 0.0)  // 1 star
    array.set(base_star_thresholds, 1, 2.0)  // 2 stars
    array.set(base_star_thresholds, 2, 4.0)  // 3 stars
    array.set(base_star_thresholds, 3, 6.0)  // 4 stars
    array.set(base_star_thresholds, 4, 8.0)  // 5 stars

// Get star rating threshold based on minimum star rating input
// Optimized with cached thresholds and reduced calculations
f_getStarRatingThreshold(star_rating) =>
    // Get base threshold from cache (with bounds checking)
    int index = math.max(0, math.min(4, star_rating - 1))
    float threshold = array.get(base_star_thresholds, index)

    // Apply dynamic adjustment based on market volatility if enabled
    if dynamicStarRating and star_rating > 1
        // Calculate current volatility relative to recent history
        float current_atr = ta.atr(dynamicATRPeriod)
        float atr_sma = ta.sma(current_atr, 50)
        float volatility_factor = atr_sma > 0 ? current_atr / atr_sma : 1.0

        // Determine market regime for additional context
        int market_regime = f_detectMarketRegime()

        // Adjust threshold based on volatility and market regime
        float adjustment_factor = 1.0

        if market_regime == 2  // Volatile market
            // Lower threshold in volatile markets to show more blocks
            adjustment_factor := math.max(0.7, 1.0 - (volatility_factor - 1.0) * volatilityAdjustment)
        else if market_regime == 1  // Trending market
            // Slightly lower threshold in trending markets
            adjustment_factor := math.max(0.85, 1.0 - (volatility_factor - 1.0) * volatilityAdjustment * 0.5)

        threshold := threshold * adjustment_factor

    threshold

// Calculate cumulative delta (buy volume - sell volume)
f_calculateDelta(length) =>
    float delta = 0.0
    for i = 0 to length - 1
        // More accurate buy/sell volume calculation
        float buyVol = high[i] != low[i] ? volume[i] * (close[i] - low[i]) / (high[i] - low[i]) : 
                       close[i] > open[i] ? volume[i] : 0
        float sellVol = high[i] != low[i] ? volume[i] * (high[i] - close[i]) / (high[i] - low[i]) : 
                         close[i] < open[i] ? volume[i] : 0
        delta += (buyVol - sellVol)
    delta

// Advanced liquidity strength calculation with multiple factors
f_calculateLiquidityStrength(price_level, is_high, volume_at_level, tests_count, age_bars) =>
    // Base strength from volume ratio
    avg_volume = ta.sma(volume, 20)
    volume_strength = volume_at_level / avg_volume
    
    // Test frequency strength (more tests = stronger level)
    test_strength = math.min(tests_count / 3.0, 2.0)
    
    // Age factor (newer levels get slight boost, older levels decay)
    age_factor = age_bars < 50 ? 1.2 : age_bars < 100 ? 1.0 : age_bars < 200 ? 0.8 : 0.6
    
    // Price action strength (distance from current price)
    distance_factor = math.abs(price_level - close) / ta.atr(20)
    proximity_strength = distance_factor < 1.0 ? 2.0 : distance_factor < 2.0 ? 1.5 : distance_factor < 5.0 ? 1.0 : 0.7
    
    // Enhanced confluence with multiple technical indicators
    confluence_strength = 1.0
    if useRSI
        rsi_val = ta.rsi(close, rsiPeriod)
        if (is_high and rsi_val > rsiOverbought) or (not is_high and rsi_val < rsiOversold)
            confluence_strength += 0.4
        else if (is_high and rsi_val > 60) or (not is_high and rsi_val < 40)
            confluence_strength += 0.2
    
    if useMACD
        [macd_line, signal_line, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
        if (is_high and macd_line < signal_line) or (not is_high and macd_line > signal_line)
            confluence_strength += 0.3
    
    // Support/Resistance confluence
    ema200_val = ta.ema(close, 200)
    if math.abs(price_level - ema200_val) / ta.atr(20) < 1.0
        confluence_strength += 0.2
    
    // Use configurable weights for final calculation
    if enhancedStrengthCalc
        final_strength = (volume_strength * volumeWeight + 
                         test_strength * testFrequencyWeight + 
                         proximity_strength * proximityWeight + 
                         confluence_strength * confluenceWeight) * 
                         (1.0 + ageWeight * (age_factor - 1.0))
    else
        // Fallback to original calculation
        final_strength = (volume_strength * 0.4 + test_strength * 0.3 + proximity_strength * 0.2 + confluence_strength * 0.1) * age_factor
    
    math.max(0.1, math.min(10.0, final_strength))

// Smart liquidity level clustering to avoid overlaps
f_clusterLiquidityLevels(array<LiquidityLevel> levels) =>
    if array.size(levels) <= 1
        levels
    
    cluster_threshold = enableSmartClustering ? ta.atr(20) * (clusteringThreshold / 100.0) : ta.atr(20) * 0.2
    clustered_levels = array.new<LiquidityLevel>()
    
    for i = 0 to array.size(levels) - 1
        current_level = array.get(levels, i)
        bool should_cluster = false
        int cluster_index = -1
        
        // Check if this level should be clustered with existing ones
        for j = 0 to array.size(clustered_levels) - 1
            existing_level = array.get(clustered_levels, j)
            if math.abs(current_level.level - existing_level.level) < cluster_threshold and current_level.is_high == existing_level.is_high
                should_cluster := true
                cluster_index := j
                break
        
        if should_cluster and cluster_index >= 0
            // Merge with existing cluster - keep stronger level
            existing_level = array.get(clustered_levels, cluster_index)
            if current_level.strength > existing_level.strength
                // Replace with stronger level but combine stats
                current_level.tests += existing_level.tests
                current_level.total_volume += existing_level.total_volume
                array.set(clustered_levels, cluster_index, current_level)
        else
            // Add as new level
            array.push(clustered_levels, current_level)
    
    clustered_levels

// Enhanced liquidity pool validation with multiple criteria
f_validateLiquidityPool(float level, bool is_high, int tests, float volume_ratio) =>
    if not validationEnabled
        true  // Skip validation if disabled
    else
        // Volume validation
        volume_valid = volume_ratio >= minVolumeThreshold
        
        // Test count validation
        tests_valid = tests >= minTestCount
        
        // Price action validation (level should be respected)
        price_action_valid = true
        if priceActionValidation
            breach_count = 0
            breach_threshold = 0.002  // 0.2% breach tolerance
            
            for i = 0 to 20
                if is_high and close[i] > level * (1.0 + breach_threshold)
                    breach_count += 1
                else if not is_high and close[i] < level * (1.0 - breach_threshold)
                    breach_count += 1
            
            price_action_valid := breach_count <= 2  // Allow max 2 minor breaches
        
        // Time validation (level should be recent enough to be relevant)
        time_valid = bar_index - tests < maxLevelAge
        
        // Quality score calculation for adaptive thresholds
        quality_score = 0.0
        if volume_valid
            quality_score += 0.3
        if tests_valid
            quality_score += 0.3
        if price_action_valid
            quality_score += 0.2
        if time_valid
            quality_score += 0.2
        
        // Apply adaptive quality threshold
        final_threshold = adaptiveQuality ? qualityThreshold * (ta.atr(20) / ta.atr(50)) : qualityThreshold
        
        volume_valid and tests_valid and price_action_valid and time_valid and quality_score >= final_threshold

// Detect liquidity pools (areas where stops cluster)
f_detectLiquidityPool(isHigh, lookback, minAge, minRetests) =>
    float level = na
    int tests = 0
    bool validPool = false
    
    if isHigh
        // Find significant highs that haven't been breached
        for i = minAge to lookback
            highLevel = high[i]
            isLocalHigh = true
            
            // Check if it's a local high
            for j = 1 to 5
                if i - j >= 0 and i + j < bar_index
                    if high[i - j] > highLevel or high[i + j] > highLevel
                        isLocalHigh := false
                        break
            
            if isLocalHigh
                // Count how many times this level was tested
                testCount = 0
                for k = 0 to i - 1
                    if high[k] >= highLevel * 0.997 and high[k] <= highLevel * 1.003
                        testCount += 1
                
                // Check if level still holds
                levelHolds = true
                for m = 0 to i - 1
                    if close[m] > highLevel * 1.001
                        levelHolds := false
                        break
                
                if levelHolds and testCount >= minRetests
                    level := highLevel
                    tests := testCount
                    validPool := true
                    break
    else
        // Find significant lows that haven't been breached
        for i = minAge to lookback
            lowLevel = low[i]
            isLocalLow = true
            
            // Check if it's a local low
            for j = 1 to 5
                if i - j >= 0 and i + j < bar_index
                    if low[i - j] < lowLevel or low[i + j] < lowLevel
                        isLocalLow := false
                        break
            
            if isLocalLow
                // Count how many times this level was tested
                testCount = 0
                for k = 0 to i - 1
                    if low[k] <= lowLevel * 1.003 and low[k] >= lowLevel * 0.997
                        testCount += 1
                
                // Check if level still holds
                levelHolds = true
                for m = 0 to i - 1
                    if close[m] < lowLevel * 0.999
                        levelHolds := false
                        break
                
                if levelHolds and testCount >= minRetests
                    level := lowLevel
                    tests := testCount
                    validPool := true
                    break
    
    [validPool, level, tests]

// ====================================================================
// ENHANCED LIQUIDITY FUNCTIONS
// ====================================================================

// Enhanced LiquidityLevel type
type LiquidityLevel
    float level
    int tests
    bool is_high
    int created_bar
    bool is_swept
    float strength
    float total_volume
    box zone_box
    line level_line
    label level_label


// Create liquidity zone visualization
f_createLiquidityZone(float level, bool is_high, float strength, int bar_idx) =>
    if liquidityStyle == "Zones"
        // Dynamic zone height based on strength and user settings
        base_height = ta.atr(20) * (strength > 3.0 ? strongZoneHeight : zoneHeight)
        top_level = is_high ? level : level + base_height
        bottom_level = is_high ? level - base_height : level
        
        // Enhanced color system with strength-based gradients
        base_color = is_high ? highLiquidityColor : lowLiquidityColor
        transparency = strength > 3.0 ? strongZoneTransparency : zoneTransparency
        
        if enhancedColors
            // Gradient colors based on strength using custom colors
            if strength > 4.0
                base_color := strongLiquidityColor
            else if strength < 2.0
                base_color := weakLiquidityColor
            else
                base_color := is_high ? highLiquidityColor : lowLiquidityColor
        
        zone_color = color.new(base_color, transparency)
        border_color = showZoneBorders ? color.new(base_color, math.max(transparency - 30, 0)) : na
        border_width_val = showZoneBorders ? zoneBorderWidth : 0
        
        // Create the zone box
        zone_box = box.new(bar_idx - 10, top_level, bar_idx + liquidityExtension, bottom_level, 
                          bgcolor=zone_color, border_color=border_color, 
                          border_width=border_width_val, extend=extend.right)
        
        // Add comprehensive label with strength, direction, and price if enabled
        if showStrengthLabels or showLevelPrice
            label_text = ""
            
            // Add strength info
            if showStrengthLabels and strength > liquidityMinStrength
                label_text := "💪 " + str.tostring(math.round(strength, 1))
            
            // Add direction arrow
            if showLiquidityDirection
                direction_arrow = is_high ? " ⬇️" : " ⬆️"
                label_text := label_text + direction_arrow
            
            // Add price info
            if showLevelPrice
                price_text = ""
                if priceFormat == "2 Decimals"
                    price_text := str.tostring(math.round(level, 2))
                else if priceFormat == "4 Decimals"
                    price_text := str.tostring(math.round(level, 4))
                else if priceFormat == "Full"
                    price_text := str.tostring(level)
                else  // Auto
                    price_text := str.tostring(math.round(level, syminfo.mintick < 0.01 ? 4 : 2))
                
                label_text := label_text + (label_text != "" ? "\n" : "") + "💰 " + price_text
            
            // Add timestamp if enabled
            if showTimeStamp
                time_text = str.tostring(bar_idx)
                label_text := label_text + (label_text != "" ? "\n" : "") + "⏰ " + time_text
            
            if label_text != ""
                label_color = color.new(base_color, 20)
                label.new(bar_idx, level, label_text, 
                         color=label_color, textcolor=color.white, 
                         style=label.style_label_center, size=size.tiny)
        
        zone_box
    else
        na

// Create liquidity line visualization
f_createLiquidityLine(float level, bool is_high, float strength, int bar_idx) =>
    if liquidityStyle == "Lines"
        // Enhanced color system with strength-based gradients
        base_color = is_high ? highLiquidityColor : lowLiquidityColor
        
        if enhancedColors
            // Gradient colors based on strength using custom colors
            if strength > 4.0
                base_color := strongLiquidityColor
            else if strength < 2.0
                base_color := weakLiquidityColor
            else
                base_color := is_high ? highLiquidityColor : lowLiquidityColor
        
        // Dynamic line width based on strength
        line_width_val = strength > 3.0 ? strongLineWidth : lineWidth
        
        // Line style selection
        line_style_val = lineStyle == "Solid" ? line.style_solid : 
                         lineStyle == "Dashed" ? line.style_dashed : line.style_dotted
        
        // Create the liquidity line(s) based on pattern
        liquidity_line = line.new(bar_idx - 10, level, bar_idx + liquidityExtension, level, 
                                 color=base_color, width=line_width_val, extend=extend.right, 
                                 style=line_style_val)
        
        // Add additional lines for Double/Triple patterns
        if linePattern == "Double" or linePattern == "Triple"
            offset = ta.atr(20) * 0.02  // Small offset for parallel lines
            secondary_color = color.new(base_color, math.min(levelOpacity + 20, 95))
            
            // Second line
            line.new(bar_idx - 10, level + offset, bar_idx + liquidityExtension, level + offset, 
                     color=secondary_color, width=math.max(line_width_val - 1, 1), 
                     extend=extend.right, style=line_style_val)
            
            if linePattern == "Triple"
                // Third line
                line.new(bar_idx - 10, level - offset, bar_idx + liquidityExtension, level - offset, 
                         color=secondary_color, width=math.max(line_width_val - 1, 1), 
                         extend=extend.right, style=line_style_val)
        
        // Add comprehensive label with strength, direction, and price if enabled
        if showStrengthLabels or showLevelPrice
            label_text = ""
            
            // Add strength info
            if showStrengthLabels and strength > liquidityMinStrength
                label_text := "💪 " + str.tostring(math.round(strength, 1))
            
            // Add direction arrow
            if showLiquidityDirection
                direction_arrow = is_high ? " ⬇️" : " ⬆️"
                label_text := label_text + direction_arrow
            
            // Add price info
            if showLevelPrice
                price_text = ""
                if priceFormat == "2 Decimals"
                    price_text := str.tostring(math.round(level, 2))
                else if priceFormat == "4 Decimals"
                    price_text := str.tostring(math.round(level, 4))
                else if priceFormat == "Full"
                    price_text := str.tostring(level)
                else  // Auto
                    price_text := str.tostring(math.round(level, syminfo.mintick < 0.01 ? 4 : 2))
                
                label_text := label_text + (label_text != "" ? "\n" : "") + "💰 " + price_text
            
            // Add timestamp if enabled
            if showTimeStamp
                time_text = str.tostring(bar_idx)
                label_text := label_text + (label_text != "" ? "\n" : "") + "⏰ " + time_text
            
            if label_text != ""
                label_color = color.new(base_color, 20)
                label.new(bar_idx + 5, level, label_text, 
                         color=label_color, textcolor=color.white, 
                         style=is_high ? label.style_label_down : label.style_label_up, 
                         size=size.tiny)
        
        liquidity_line
    else
        na

// Create liquidity marker visualization
f_createLiquidityMarker(float level, bool is_high, float strength, int tests, int bar_idx) =>
    if liquidityStyle == "Markers"
        // Enhanced color system using custom colors
        base_color = is_high ? highLiquidityColor : lowLiquidityColor
        
        if enhancedColors
            if strength > 4.0
                base_color := strongLiquidityColor
            else if strength < 2.0
                base_color := weakLiquidityColor
            else
                base_color := is_high ? highLiquidityColor : lowLiquidityColor
        
        // Enhanced marker text with strength and direction indicators
        marker_text = is_high ? "LH" : "LL"
        marker_text := marker_text + "\n" + str.tostring(tests) + "x"
        
        // Add strength indicator
        if showStrengthLabels
            marker_text := marker_text + "\n💪" + str.tostring(math.round(strength, 1))
        
        // Add quality metrics when enabled
        if showQualityMetrics
            // Calculate quality score components
            volume_score = math.min(1.0, strength / 3.0)  // Normalize strength to 0-1
            test_score = math.min(1.0, tests / 5.0)       // Normalize tests to 0-1
            age_score = math.max(0.1, 1.0 - ((bar_index - bar_idx) / 200.0))  // Age factor
            
            overall_quality = (volume_score + test_score + age_score) / 3.0
            quality_stars = overall_quality > 0.8 ? "⭐⭐⭐" : 
                           overall_quality > 0.6 ? "⭐⭐" : 
                           overall_quality > 0.4 ? "⭐" : "💤"
            
            marker_text := marker_text + "\n" + quality_stars
            
            // Add quality percentage for detailed view
            if overall_quality >= qualityThreshold
                quality_percent = math.round(overall_quality * 100, 0)
                marker_text := marker_text + "\n" + str.tostring(quality_percent) + "%"
        
        // Add direction arrow
        if showLiquidityDirection
            direction_arrow = is_high ? "⬇️" : "⬆️"
            marker_text := direction_arrow + " " + marker_text
        
        // Strength-based icon
        if strength > 4.0
            marker_text := "🔥" + marker_text
        else if strength > 3.0
            marker_text := "⚡" + marker_text
        else if strength < 2.0
            marker_text := "💤" + marker_text
        
        // Enhanced label styling based on quality
        label_size = showQualityMetrics and strength > 3.0 ? size.normal : size.small
        label_transparency = showQualityMetrics ? math.max(10, 90 - int(strength * 15)) : 20
        
        label.new(bar_idx, level, marker_text, 
                 color=color.new(base_color, label_transparency), textcolor=color.white, 
                 style=is_high ? label.style_label_down : label.style_label_up, 
                 size=label_size)
    else
        na

// Enhanced liquidity sweep detection
f_detectLiquiditySweep(LiquidityLevel liq_level) =>
    bool swept = false
    
    if not enhancedSweepDetection
        // Simple sweep detection (original logic)
        if liq_level.is_high
            swept := close > liq_level.level and not liq_level.is_swept
        else
            swept := close < liq_level.level and not liq_level.is_swept
    else
        // Enhanced sweep detection with confirmation and volume validation
        if not liq_level.is_swept
            breach_confirmed = false
            volume_confirmed = false
            
            // Check for price breach with confirmation bars
            consecutive_breaches = 0
            for i = 0 to sweepConfirmationBars - 1
                if liq_level.is_high
                    if close[i] > liq_level.level * 1.001  // 0.1% buffer for noise
                        consecutive_breaches += 1
                else
                    if close[i] < liq_level.level * 0.999  // 0.1% buffer for noise
                        consecutive_breaches += 1
            
            breach_confirmed := consecutive_breaches >= sweepConfirmationBars
            
            // Volume confirmation for sweep
            if breach_confirmed
                avg_volume = ta.sma(volume, 20)
                recent_volume = 0.0
                for i = 0 to sweepConfirmationBars - 1
                    recent_volume += volume[i]
                
                avg_recent_volume = recent_volume / sweepConfirmationBars
                volume_confirmed := avg_recent_volume >= (avg_volume * sweepVolumeThreshold)
            
            swept := breach_confirmed and volume_confirmed
    
    swept



// Calculate pulse effect for new blocks
f_getPulseEffect(int created_bar, int current_bar) =>
    if pulseNewBlocks and (current_bar - created_bar) < 10
        float pulse_factor = math.sin((current_bar - created_bar) * 0.5) * 0.3 + 0.7
        math.max(0.4, pulse_factor)
    else
        1.0



// Simple heatmap legend
f_createHeatmapLegend() =>
    if liquidityStyle == "Heatmap" and enableLiquidityPools and showHeatmapLegend
        // Create legend table
        legend_table = table.new(position.top_right, 2, 5, 
                                 bgcolor=color.new(color.white, 90),
                                 border_width=1,
                                 border_color=color.gray)
        
        // Header
        table.cell(legend_table, 0, 0, "Liquidity Heatmap", 
                  text_color=color.white, text_size=size.small, bgcolor=color.new(color.gray, 80))
        table.merge_cells(legend_table, 0, 0, 1, 0)
        
        // Enhanced legend entries with detailed percentage ranges and labels
        table.cell(legend_table, 0, 1, "🔴", text_color=color.red, text_size=size.normal)
        table.cell(legend_table, 1, 1, "MAX (80%+) - Highest Liquidity", text_color=color.white, text_size=size.tiny)
        
        table.cell(legend_table, 0, 2, "🟠", text_color=color.orange, text_size=size.normal)
        table.cell(legend_table, 1, 2, "HIGH (60-80%) - Strong Liquidity", text_color=color.white, text_size=size.tiny)
        
        table.cell(legend_table, 0, 3, "🟡", text_color=color.yellow, text_size=size.normal)
        table.cell(legend_table, 1, 3, "MEDIUM (40-60%) - Moderate Liquidity", text_color=color.white, text_size=size.tiny)
        
        table.cell(legend_table, 0, 4, "🔵", text_color=color.blue, text_size=size.normal)
        table.cell(legend_table, 1, 4, "LOW (25-40%) - Lower Liquidity", text_color=color.white, text_size=size.tiny)
        
        table.cell(legend_table, 0, 5, "ℹ️", text_color=color.gray, text_size=size.normal)
        table.cell(legend_table, 1, 5, "Labels show exact % values", text_color=color.white, text_size=size.tiny)

// Create liquidity heatmap visualization
f_createLiquidityHeatmap(array<LiquidityLevel> levels, int bar_idx) =>
    if liquidityStyle == "Heatmap" and array.size(levels) > 0
        // Calculate price range from liquidity levels
        float min_price = high
        float max_price = low
        
        for i = 0 to array.size(levels) - 1
            level = array.get(levels, i)
            min_price := math.min(min_price, level.level)
            max_price := math.max(max_price, level.level)
        
        // Use user-configurable zone count for visualization
        zone_count = heatmapZoneCount
        price_range = max_price - min_price
        zone_size = price_range / zone_count
        
        // Initialize zone liquidity array
        var float[] zone_liquidity = array.new_float(0)
        array.clear(zone_liquidity)
        
        for i = 0 to zone_count - 1
            array.push(zone_liquidity, 0.0)
        
        // Calculate liquidity for each zone
        for i = 0 to array.size(levels) - 1
            level = array.get(levels, i)
            zone_index = math.floor((level.level - min_price) / zone_size)
            
            if zone_index >= 0 and zone_index < zone_count
                // Weight by strength, tests, and age
                age_factor = math.max(0.1, 1.0 - ((bar_idx - level.created_bar) / 200.0))
                weight_multiplier = level.strength * age_factor * level.tests
                
                current_liq = array.get(zone_liquidity, int(zone_index))
                array.set(zone_liquidity, int(zone_index), current_liq + weight_multiplier)
        
        // Find max liquidity for normalization
        max_liquidity = 0.0
        for i = 0 to zone_count - 1
            liquidity = array.get(zone_liquidity, i)
            if liquidity > max_liquidity
                max_liquidity := liquidity
        
        // Draw heatmap zones with original simple approach
        if max_liquidity > 0
            for i = 0 to zone_count - 1
                zone_bottom = min_price + (i * zone_size)
                zone_top = zone_bottom + zone_size
                liquidity = array.get(zone_liquidity, i)
                intensity = liquidity / max_liquidity
                
                if intensity > (heatmapZoneThreshold / 100.0)  // Only show zones above threshold
                    transparency = int(85 - (intensity * 50))
                    
                    // Simple, clean color scheme
                    zone_color = intensity > 0.8 ? color.new(color.red, transparency) :      // Highest liquidity
                                 intensity > 0.6 ? color.new(color.orange, transparency) :   // High liquidity  
                                 intensity > 0.4 ? color.new(color.yellow, transparency) :   // Medium liquidity
                                 color.new(color.blue, transparency)                         // Lower liquidity
                    
                    box.new(
                         bar_idx - 20, zone_top, 
                         bar_idx + heatmapBars, zone_bottom,
                         bgcolor=zone_color,
                         border_color=color.new(color.gray, 70),
                         border_width=1,
                         extend=extend.right
                         )
                    
                    // Enhanced intensity labels with user-controlled display
                    if showHeatmapLabels and (intensity * 100) >= heatmapLabelThreshold
                        label_text = ""
                        label_color = color.black
                        text_color = color.white
                        
                        intensity_percent = math.round(intensity * 100, 0)
                        
                        if intensity > 0.8
                            label_text := str.tostring(intensity_percent) + "% MAX"
                            label_color := color.new(color.red, 10)
                            text_color := color.white
                        else if intensity > 0.6
                            label_text := str.tostring(intensity_percent) + "% HIGH"
                            label_color := color.new(color.orange, 15)
                            text_color := color.white
                        else if intensity > 0.4
                            label_text := str.tostring(intensity_percent) + "% MED"
                            label_color := color.new(color.yellow, 20)
                            text_color := color.black
                        else
                            label_text := str.tostring(intensity_percent) + "%"
                            label_color := color.new(color.blue, 25)
                            text_color := color.white
                        
                        label.new(
                             bar_idx + heatmapBars - 8, (zone_top + zone_bottom) / 2,
                             label_text,
                             style=label.style_label_left,
                             color=label_color,
                             textcolor=text_color,
                             size=size.tiny
                             )

// Enhanced update and management of liquidity levels
f_updateLiquidityLevels(array<LiquidityLevel> levels) =>
    // Clean old levels (older than 200 bars) and invalid levels
    if array.size(levels) > 0
        for i = array.size(levels) - 1 to 0
            level = array.get(levels, i)
            age_bars = bar_index - level.created_bar
            
            // Enhanced cleanup criteria
            should_remove = age_bars > 200 or 
                           level.is_swept or 
                           not f_validateLiquidityPool(level.level, level.is_high, level.tests, level.total_volume / ta.sma(volume, 20))
            
            if should_remove
                // Clean up visual elements
                if not na(level.zone_box)
                    box.delete(level.zone_box)
                if not na(level.level_line)
                    line.delete(level.level_line)
                if not na(level.level_label)
                    label.delete(level.level_label)
                array.remove(levels, i)
    
    // Update existing levels with enhanced strength calculation
    if array.size(levels) > 0
        for i = 0 to array.size(levels) - 1
            level = array.get(levels, i)
            age_bars = bar_index - level.created_bar
            
            // Recalculate strength with enhanced algorithm
            level.strength := f_calculateLiquidityStrength(level.level, level.is_high, level.total_volume, level.tests, age_bars)
            
            // Enhanced sweep detection
            if f_detectLiquiditySweep(level)
                level.is_swept := true
                
                // Enhanced sweep label styling based on user preference
                if showSweepLabels and not na(level.level_label)
                    sweep_text = ""
                    sweep_color = color.orange
                    
                    if sweepLabelStyle == "Simple"
                        sweep_text := "SWEPT"
                    else if sweepLabelStyle == "Detailed"
                        direction = level.is_high ? "↗️" : "↘️"
                        strength_text = level.strength > 3.0 ? "STRONG" : level.strength > 2.0 ? "MED" : "WEAK"
                        sweep_text := direction + " SWEPT " + strength_text
                        sweep_color := level.strength > 3.0 ? color.red : level.strength > 2.0 ? color.orange : color.yellow
                    else  // Minimal
                        sweep_text := level.is_high ? "↗️" : "↘️"
                    
                    label.set_text(level.level_label, sweep_text)
                    label.set_color(level.level_label, color.new(sweep_color, 15))
                    label.set_textcolor(level.level_label, color.white)
                    label.set_style(level.level_label, label.style_label_center)
    
    // Find new liquidity levels with enhanced detection
    temp_new_levels = array.new<LiquidityLevel>()
    
    // High liquidity detection
    for lookback_period = liquidityMinAge to liquidityLookback
        if lookback_period < bar_index
            pivot_high = ta.pivothigh(high, 5, 5)
            if not na(pivot_high) and pivot_high == high[5]
                // Enhanced duplicate check with clustering threshold
                bool level_exists = false
                cluster_threshold = ta.atr(20) * 0.15
                
                if array.size(levels) > 0
                    for i = 0 to array.size(levels) - 1
                        existing_level = array.get(levels, i)
                        if existing_level.is_high and math.abs(existing_level.level - pivot_high) < cluster_threshold
                            level_exists := true
                            existing_level.tests += 1
                            existing_level.total_volume += volume[5]
                            // Recalculate strength
                            age_bars = bar_index - existing_level.created_bar
                            existing_level.strength := f_calculateLiquidityStrength(existing_level.level, true, existing_level.total_volume, existing_level.tests, age_bars)
                            break
                
                if not level_exists
                    // Enhanced strength calculation
                    level_strength = f_calculateLiquidityStrength(pivot_high, true, volume[5], 1, 0)
                    
                    // Validate the new level before adding
                    volume_ratio = volume[5] / ta.sma(volume, 20)
                    if f_validateLiquidityPool(pivot_high, true, 1, volume_ratio)
                        // Create new liquidity level
                        new_level = LiquidityLevel.new(
                             level = pivot_high,
                             tests = 1,
                             is_high = true,
                             created_bar = bar_index - 5,
                             is_swept = false,
                             strength = level_strength,
                             total_volume = volume[5],
                             zone_box = f_createLiquidityZone(pivot_high, true, level_strength, bar_index - 5),
                             level_line = f_createLiquidityLine(pivot_high, true, level_strength, bar_index - 5),
                             level_label = f_createLiquidityMarker(pivot_high, true, level_strength, 1, bar_index - 5)
                             )
                        
                        array.push(temp_new_levels, new_level)
    
    // Low liquidity detection
    for lookback_period = liquidityMinAge to liquidityLookback
        if lookback_period < bar_index
            pivot_low = ta.pivotlow(low, 5, 5)
            if not na(pivot_low) and pivot_low == low[5]
                // Enhanced duplicate check with clustering threshold
                bool level_exists = false
                cluster_threshold = ta.atr(20) * 0.15
                
                if array.size(levels) > 0
                    for i = 0 to array.size(levels) - 1
                        existing_level = array.get(levels, i)
                        if not existing_level.is_high and math.abs(existing_level.level - pivot_low) < cluster_threshold
                            level_exists := true
                            existing_level.tests += 1
                            existing_level.total_volume += volume[5]
                            // Recalculate strength
                            age_bars = bar_index - existing_level.created_bar
                            existing_level.strength := f_calculateLiquidityStrength(existing_level.level, false, existing_level.total_volume, existing_level.tests, age_bars)
                            break
                
                if not level_exists
                    // Enhanced strength calculation
                    level_strength = f_calculateLiquidityStrength(pivot_low, false, volume[5], 1, 0)
                    
                    // Validate the new level before adding
                    volume_ratio = volume[5] / ta.sma(volume, 20)
                    if f_validateLiquidityPool(pivot_low, false, 1, volume_ratio)
                        // Create new liquidity level
                        new_level = LiquidityLevel.new(
                             level = pivot_low,
                             tests = 1,
                             is_high = false,
                             created_bar = bar_index - 5,
                             is_swept = false,
                             strength = level_strength,
                             total_volume = volume[5],
                             zone_box = f_createLiquidityZone(pivot_low, false, level_strength, bar_index - 5),
                             level_line = f_createLiquidityLine(pivot_low, false, level_strength, bar_index - 5),
                             level_label = f_createLiquidityMarker(pivot_low, false, level_strength, 1, bar_index - 5)
                             )
                        
                        array.push(temp_new_levels, new_level)
    
    // Apply smart clustering to new levels before adding them
    if array.size(temp_new_levels) > 0
        clustered_new_levels = f_clusterLiquidityLevels(temp_new_levels)
        
        // Add clustered new levels to main array
        for i = 0 to array.size(clustered_new_levels) - 1
            array.push(levels, array.get(clustered_new_levels, i))
    
    // Apply clustering to all levels to maintain clean visualization
    if array.size(levels) > 1
        clustered_levels = f_clusterLiquidityLevels(levels)
        
        // Replace the original array with clustered version
        array.clear(levels)
        for i = 0 to array.size(clustered_levels) - 1
            array.push(levels, array.get(clustered_levels, i))

// Create quality metrics panel for liquidity analysis
f_createQualityMetricsPanel(array<LiquidityLevel> levels) =>
    if array.size(levels) > 0
        // Calculate overall statistics
        int total_levels = array.size(levels)
        int active_levels = 0
        int swept_levels = 0
        int high_quality_levels = 0
        float avg_strength = 0.0
        float total_strength = 0.0
        float max_strength = 0.0
        int total_tests = 0
        
        // Analyze all levels
        for i = 0 to total_levels - 1
            level = array.get(levels, i)
            total_strength += level.strength
            max_strength := math.max(max_strength, level.strength)
            total_tests += level.tests
            
            if level.is_swept
                swept_levels += 1
            else
                active_levels += 1
            
            if level.strength >= qualityThreshold
                high_quality_levels += 1
        
        avg_strength := total_levels > 0 ? total_strength / total_levels : 0.0
        float avg_tests = total_levels > 0 ? total_tests / total_levels : 0.0
        float quality_ratio = total_levels > 0 ? (high_quality_levels / total_levels) * 100 : 0.0
        float sweep_ratio = total_levels > 0 ? (swept_levels / total_levels) * 100 : 0.0
        
        // Create quality metrics table
        var table qualityTable = table.new(
            position.top_left,
            2, 9,
            bgcolor = color.new(color.black, 20),
            border_width = 1,
            border_color = color.new(color.white, 50)
        )
        
        // Header
        table.cell(qualityTable, 0, 0, "📊 LIQUIDITY METRICS", 
                  text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 10), 
                  text_size=size.small)
        table.cell(qualityTable, 1, 0, "VALUES", 
                  text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 10), 
                  text_size=size.small)
        
        // Metrics rows
        table.cell(qualityTable, 0, 1, "Total Levels", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 1, str.tostring(total_levels), 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        
        table.cell(qualityTable, 0, 2, "Active Levels", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 2, str.tostring(active_levels), 
                  text_color=color.lime, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        
        table.cell(qualityTable, 0, 3, "Swept Levels", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 3, str.tostring(swept_levels), 
                  text_color=color.orange, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        
        table.cell(qualityTable, 0, 4, "High Quality", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 4, str.tostring(high_quality_levels) + " (" + str.tostring(quality_ratio, "#.#") + "%)", 
                  text_color=color.yellow, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        
        table.cell(qualityTable, 0, 5, "Avg Strength", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 5, str.tostring(avg_strength, "#.##"), 
                  text_color=color.aqua, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        
        table.cell(qualityTable, 0, 6, "Max Strength", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 6, str.tostring(max_strength, "#.##"), 
                  text_color=color.fuchsia, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        
        table.cell(qualityTable, 0, 7, "Avg Tests", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 7, str.tostring(avg_tests, "#.#"), 
                  text_color=color.silver, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        
        table.cell(qualityTable, 0, 8, "Sweep Rate", 
                  text_color=color.white, bgcolor=color.new(color.gray, 80), text_size=size.tiny)
        table.cell(qualityTable, 1, 8, str.tostring(sweep_ratio, "#.#") + "%", 
                  text_color=sweep_ratio > 50 ? color.red : color.green, bgcolor=color.new(color.gray, 80), text_size=size.tiny)

// Display liquidity levels based on selected style
f_displayLiquidityLevels(array<LiquidityLevel> levels) =>
    if enableLiquidityPools and array.size(levels) > 0
        if liquidityStyle == "Heatmap"
            f_createLiquidityHeatmap(levels, bar_index)
        // Other styles are handled during level creation

// Detect order flow imbalance zones
f_detectOrderFlowImbalance(period, threshold, minVolRatio) =>
    // Calculate delta over period
    currentDelta = f_calculateDelta(period)
    smoothedDelta = ta.sma(currentDelta, deltaSmoothing)
    
    // Calculate average volume
    avgVolume = ta.sma(volume, 20)
    periodVolume = math.sum(volume, period)
    volRatio = periodVolume / (avgVolume * period)
    
    // Calculate buy/sell pressure
    buyPressure = 0.0
    sellPressure = 0.0
    for i = 0 to period - 1
        if close[i] > open[i]
            buyPressure += volume[i]
        else
            sellPressure += volume[i]
    
    totalPressure = buyPressure + sellPressure
    buyPercent = totalPressure > 0 ? (buyPressure / totalPressure) * 100 : 50
    sellPercent = totalPressure > 0 ? (sellPressure / totalPressure) * 100 : 50
    
    // Detect imbalance
    bool bullishImbalance = buyPercent >= threshold and volRatio >= minVolRatio
    bool bearishImbalance = sellPercent >= threshold and volRatio >= minVolRatio
    
    // Detect delta divergence
    priceChange = close - close[period]
    deltaDirection = smoothedDelta > 0 ? 1 : -1
    priceDirection = priceChange > 0 ? 1 : -1
    bool divergence = deltaDirection != priceDirection and math.abs(smoothedDelta) > avgVolume
    
    [bullishImbalance, bearishImbalance, buyPercent, sellPercent, smoothedDelta, divergence]

// Enhanced confluence detection with new factors
f_enhancedConfluence(level, is_support, has_liquidity, has_orderflow) =>
    // Original confluence checks
    rsi_conf = useRSI and detectConfluence and f_rsiConfluence(level, is_support)
    macd_conf = useMACD and detectConfluence and f_macdConfluence(level, is_support)
    ma_conf = detectConfluence and f_maConfluence(level, is_support)
    vol_conf = useVolumeProfile and f_volumeConfluence(volume)
    
    // Add new confluence factors
    liquidity_conf = enableLiquidityPools and has_liquidity
    orderflow_conf = enableOrderFlow and has_orderflow
    
    // Count total confluence factors
    confluence_count = (rsi_conf ? 1 : 0) + (macd_conf ? 1 : 0) + (ma_conf ? 1 : 0) + 
                      (vol_conf ? 1 : 0) + (liquidity_conf ? 1 : 0) + (orderflow_conf ? 1 : 0)
    
    has_confluence = confluence_count >= 2  // At least 2 confluence factors
    
    [has_confluence, confluence_count, liquidity_conf, orderflow_conf]

// ====================================================================
// THEME AND VISUAL ENHANCEMENT FUNCTIONS
// ====================================================================

// Get theme-based colors
f_getThemeColors(string theme, bool is_support) =>
    color fill_color = color.white
    color border_color = color.white
    
    if theme == "Professional"
        fill_color := is_support ? color.rgb(0, 150, 100, 80) : color.rgb(200, 50, 50, 80)
        border_color := is_support ? color.rgb(0, 200, 120) : color.rgb(255, 80, 80)
    else if theme == "Dark"
        fill_color := is_support ? color.rgb(0, 255, 150, 85) : color.rgb(255, 100, 100, 85)
        border_color := is_support ? color.rgb(0, 255, 200) : color.rgb(255, 150, 150)
    else if theme == "Neon"
        fill_color := is_support ? color.rgb(0, 255, 255, 70) : color.rgb(255, 0, 255, 70)
        border_color := is_support ? color.rgb(0, 255, 255) : color.rgb(255, 0, 255)
    else if theme == "Minimal"
        fill_color := is_support ? color.rgb(100, 100, 100, 90) : color.rgb(150, 150, 150, 90)
        border_color := is_support ? color.rgb(120, 120, 120) : color.rgb(180, 180, 180)
    else if theme == "Colorblind"
        fill_color := is_support ? color.rgb(0, 100, 200, 80) : color.rgb(200, 100, 0, 80)
        border_color := is_support ? color.rgb(0, 150, 255) : color.rgb(255, 150, 0)
    else // Custom - use user-defined colors
        fill_color := is_support ? bullColor : bearColor
        border_color := is_support ? bullBorderColor : bearBorderColor
    
    [fill_color, border_color]

// Apply gradient effect to colors
f_applyGradientEffect(color base_color, float intensity, bool is_top) =>
    if blockStyle == "Gradient" and gradientIntensity > 0
        int transparency_adjust = int(intensity * 30)
        if is_top
            color.new(base_color, color.t(base_color) + transparency_adjust)
        else
            color.new(base_color, math.max(0, color.t(base_color) - transparency_adjust))
    else
        base_color

// Apply glow effect
f_applyGlowEffect(color base_color, bool has_glow) =>
    if glowEffect and has_glow
        color.new(base_color, math.max(0, color.t(base_color) - 20))
    else
        base_color

// Get smart label position to avoid overlaps
f_getSmartLabelPosition(float price_level, int bar_idx, bool is_support) =>
    float offset_multiplier = smartLabels ? 1.5 : 1.0
    float base_offset = labelOffset * offset_multiplier
    
    // Adjust based on chart zoom and spacing
    if smartSpacing
        float zoom_factor = math.log(ta.atr(20) / ta.atr(200)) + 1
        base_offset := base_offset * math.max(0.5, math.min(2.0, zoom_factor))
    
    is_support ? price_level - base_offset : price_level + base_offset

// Get icon for label based on block type and state
f_getBlockIcon(bool is_support, bool is_broken, bool is_mitigated, bool has_confluence) =>
    if not iconLabels
        is_support ? "SUP" : "RES"
    else if is_mitigated
        "⚡"
    else if is_broken
        "💥"
    else if has_confluence
        "🎯"
    else if is_support
        "🔺"
    else
        "🔻"


// ====================================================================
// ENHANCED COLOR MANAGEMENT FUNCTIONS
// ====================================================================

// Calculate enhanced colors based on strength, volume, and state
f_getEnhancedColors(bool is_support, float strength, float vol_ratio, bool has_confluence, bool is_broken, bool is_mitigated) =>
    // Get theme-based base colors
    [theme_fill, theme_border] = f_getThemeColors(themePreset, is_support)
    
    // Base colors (use theme or custom)
    color base_fill = themePreset == "Custom" ? (is_support ? bullColor : bearColor) : theme_fill
    color base_border = themePreset == "Custom" ? (is_support ? bullBorderColor : bearBorderColor) : theme_border
    
    // State-based color adjustments
    if is_mitigated
        base_fill := mitigatedColor
        base_border := mitigatedBorderColor
    else if is_broken
        base_fill := breakoutColor
        base_border := brokenBorderColor
    
    // Apply enhanced visual effects if enabled
    if enhancedColorMode
        // Strength-based color intensity
        if strengthBasedColors and not is_mitigated and not is_broken
            float strength_factor = math.min(strength / 10.0, 1.0)
            base_transparency = color.t(base_fill)
            adjusted_transparency = int(base_transparency * (1.0 - strength_factor * 0.3))
            base_fill := color.new(color.rgb(color.r(base_fill), color.g(base_fill), color.b(base_fill)), adjusted_transparency)
        
        // Apply gradient effect
        if blockStyle == "Gradient"
            base_fill := f_applyGradientEffect(base_fill, gradientIntensity, false)
        
        // Apply glow effect for confluence or high-strength blocks
        if glowEffect and (has_confluence or strength > 7.0)
            base_fill := f_applyGlowEffect(base_fill, true)
            base_border := f_applyGlowEffect(base_border, true)
    
    [base_fill, base_border]

// Calculate border width based on volume ratio
f_getBorderWidth(float vol_ratio, bool has_confluence) =>
    int base_width = orderBlockBorderWidth
    
    if volumeBasedBorders and vol_ratio > 0
        // Reduced border width increases for high-volume zones
        if vol_ratio >= 4.0
            base_width := base_width + 1
        else if vol_ratio >= 2.5
            base_width := base_width + 0
    
    // Reduced extra width for confluence zones
    if has_confluence
        base_width := base_width + 0
    
    math.min(base_width, 3)  // Reduced cap to 3 pixels

// ====================================================================
// TYPE DEFINITIONS
// ====================================================================
    
// Enhanced OrderBlock type with additional properties
type OrderBlock
    // Core block properties
    box box
    float upper_level
    float lower_level
    bool is_support
    int left_index
    int created_at

    // Volume information
    float total_volume
    float buy_volume
    float sell_volume

    // State tracking
    bool is_broken
    bool is_mitigated
    bool is_display_hidden  // New flag to track blocks hidden for display but kept for analysis

    // Strength and quality metrics
    float strength_score
    float volume_ratio
    bool has_confluence
    int retest_count
    float success_rate
    bool strength_calculated  // Flag to track if strength has been calculated
    bool meets_min_rating     // Flag to track if block meets minimum rating

    // Confluence factors
    bool rsi_confluence
    bool macd_confluence
    bool ma_confluence
    bool volume_confluence

    // Visual elements
    line midline
    label top_price_label
    label bottom_price_label
    label confluence_label

    // Performance tracking
    bool was_successful
    int last_touched_bar
    bool success_evaluated

// Type to store the results of testing a single lookback value
type LookbackTestResult
    int lookback_value
    float total_strength
    int block_count

// ====================================================================
// UTILITY FUNCTIONS (MUST BE DEFINED BEFORE USE)
// ====================================================================

// Faster up/down volume calculation with single comparison
upAndDownVolume() =>
    close > open ? volume : -volume

// Enhanced pivot calculation with volume spike validation
calcPivots(src, lb) =>
    // Ensure lookback is valid and reasonable
    validLb = math.max(1, math.min(50, lb))  // Cap at 50 to prevent extreme values

    // Use try/catch approach for pivot calculations
    float pivotHigh = na
    float pivotLow = na

    // Calculate pivots with error handling
    if not na(src) and validLb > 0 and validLb < 100  // Additional safety check
        float temp_pivotHigh = ta.pivothigh(src, validLb, validLb)
        float temp_pivotLow = ta.pivotlow(src, validLb, validLb)

        // Validate pivots with volume spike detection if enabled
        if not na(temp_pivotHigh)
            // Pass the offset of the pivot bar
            if f_detectVolumeSpike(validLb)
                pivotHigh := temp_pivotHigh

        if not na(temp_pivotLow)
            // Pass the offset of the pivot bar
            if f_detectVolumeSpike(validLb)
                pivotLow := temp_pivotLow

    [pivotHigh, pivotLow]

// Improved support and resistance calculation with caching and error handling
calcSupportResistance(src, lb, Vol, vol_sma, current_atr) =>
    // Cache volume thresholds for efficiency
    float volThreshold = vol_sma * volMultiplier

    // Get pivot points (only calculate once)
    [pivotHigh, pivotLow] = calcPivots(src, lb)

    // Calculate box width with enhanced adaptivity
    float width = 0.0
    if useDynamicBox
        if adaptiveBoxSize
            // Use the advanced adaptive box sizing
            width := f_getAdaptiveBoxSize() // ATR will be calculated internally or use global
        else
            // Use the standard dynamic box sizing
            width := current_atr * dynamicBoxMultiplier
    else
        width := current_atr * box_width

    // Initialize variables with default values
    float supportLevel = na
    float supportLevelAlt = na
    float resistanceLevel = na
    float resistanceLevelAlt = na
    color sup_color = bullColor
    color res_color = bearColor
    bool breakout_res = false
    bool res_holds = false
    bool sup_holds = false
    bool breakout_sup = false

    // Volume requirement check
    bool volumeSignificant = nz(Vol, 0) > volThreshold

    // Set support levels if conditions met
    if not na(pivotLow) and volumeSignificant
        supportLevel := pivotLow
        supportLevelAlt := pivotLow - width

    // Set resistance levels if conditions met
    if not na(pivotHigh) and volumeSignificant
        resistanceLevel := pivotHigh
        resistanceLevelAlt := pivotHigh + width

    // Calculate breakouts and holds only if levels are valid
    if not na(resistanceLevel)
        breakout_res := ta.crossover(high, resistanceLevelAlt) //low
        res_holds := ta.crossunder(high, resistanceLevel)

    if not na(supportLevel)
        breakout_sup := ta.crossunder(low, supportLevel) //crossover low
        sup_holds := ta.crossunder(high, supportLevelAlt)

    // Return all values in a consistent order
    [supportLevel, supportLevelAlt, resistanceLevel, resistanceLevelAlt,
     sup_color, res_color, breakout_res, res_holds, sup_holds, breakout_sup, Vol]

// ====================================================================
// SIMPLE LOOK-BACK OPTIMISER - COMPLETELY REWRITTEN
// ====================================================================
TEST_WINDOW = 250
var bool OPT_DEBUG = false

// ─────────────────────────────────────────────────────────────────────
// 3) Completely new approach - count actual pivots in history
// ─────────────────────────────────────────────────────────────────────
f_findBestLBNew() =>
    int minLB = minLookbackRange
    int maxLB = maxLookbackRange
    
    // Conditionally create table only if OPT_DEBUG is enabled
    table t = na
    int row = 1
    if OPT_DEBUG
        t := table.new(position.bottom_right, 7, maxLB - minLB + 2, 
                       border_width=2,
                       border_color=color.white)
        // Clear and setup header
        table.clear(t, 0, 0)
        table.cell(t, 0, 0, "LB", bgcolor=color.rgb(20,20,20), text_color=color.yellow, text_size=tableTextSize)
        table.cell(t, 1, 0, "Pivots", bgcolor=color.rgb(20,20,20), text_color=color.yellow, text_size=tableTextSize)
        table.cell(t, 2, 0, "OBs", bgcolor=color.rgb(20,20,20), text_color=color.yellow, text_size=tableTextSize)
        table.cell(t, 3, 0, "Min Str", bgcolor=color.rgb(20,20,20), text_color=color.yellow, text_size=tableTextSize)
        table.cell(t, 4, 0, "Max Str", bgcolor=color.rgb(20,20,20), text_color=color.yellow, text_size=tableTextSize)
        table.cell(t, 5, 0, "Avg Str", bgcolor=color.rgb(20,20,20), text_color=color.yellow, text_size=tableTextSize)
        table.cell(t, 6, 0, "Total", bgcolor=color.rgb(20,20,20), text_color=color.yellow, text_size=tableTextSize)
    
    float bestAvg = 0.0
    int bestLB = lookbackPeriod
    
    // Test each lookback
    for lb = minLB to maxLB
        array<float> orderblock_strengths = array.new_float(0)
        int total_orderblocks = 0
        int total_pivots = 0
        
        // Look through recent history
        int search_start = lb + 50
        int search_end = math.min(150, bar_index - lb - 10)
        
        if search_end > search_start
            for i = search_start to search_end
                // Manual pivot detection for pivot low
                bool is_pivot_low = true
                for j = 1 to lb
                    if (i - j) >= 0 and (i + j) < bar_index
                        if low[i - j] <= low[i] or low[i + j] <= low[i]
                            is_pivot_low := false
                            break
                
                if is_pivot_low
                    total_pivots += 1
                    
                    float vol_at_i = nz(volume[i], 0)
                    float vol_sma_at_i = nz(ta.sma(volume, vol_len)[i], 1)
                    
                    if vol_at_i > vol_sma_at_i * 1.1
                        float vol_ratio = vol_at_i / math.max(1.0, vol_sma_at_i)
                        
                        // More varied strength calculation
                        float base_strength = vol_ratio // Use direct volume ratio (will vary more)
                        float confluence_bonus = 0.0
                        int confluence_count = 0
                        
                        // Add confluence bonuses with more variation
                        if useRSI and i >= rsiPeriod
                            float rsi_val = ta.rsi(close, rsiPeriod)[i]
                            if rsi_val <= rsiOversold
                                confluence_bonus += 1.0
                                confluence_count += 1
                            else if rsi_val <= 40  // Partial bonus for moderately low RSI
                                confluence_bonus += 0.5
                                confluence_count += 1
                        
                        if useMACD and i >= macdSlowLength
                            [macdLine, signalLine, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
                            if macdLine[i] < 0 and macdLine[i] > signalLine[i]
                                confluence_bonus += 1.0
                                confluence_count += 1
                            else if macdLine[i] < 0  // Partial bonus for MACD below zero
                                confluence_bonus += 0.3
                        
                        if detectConfluence and i >= ema200_length
                            float ema200_val = ta.ema(ema200_source, ema200_length)[i]
                            float distance = math.abs(low[i] - ema200_val)
                            float atr_val = ta.atr(dynamicATRPeriod)[i]
                            
                            if distance <= atr_val * 0.5
                                confluence_bonus += 1.5  // Very close to EMA
                                confluence_count += 1
                            else if distance <= atr_val
                                confluence_bonus += 0.8  // Moderately close
                            else if distance <= atr_val * 2
                                confluence_bonus += 0.3  // Somewhat close
                        
                        // Volume confluence with graduated scoring
                        if vol_ratio >= minVolumeRatio * 2
                            confluence_bonus += 1.5  // Very high volume
                        else if vol_ratio >= minVolumeRatio
                            confluence_bonus += 1.0  // Good volume
                        else if vol_ratio >= minVolumeRatio * 0.7
                            confluence_bonus += 0.5  // Decent volume
                        
                        // Final strength with more realistic scaling
                        float strength = base_strength + confluence_bonus
                        
                        if strength > 0
                            array.push(orderblock_strengths, strength)
                            total_orderblocks += 1
                
                // Manual pivot detection for pivot high (similar logic)
                bool is_pivot_high = true
                for j = 1 to lb
                    if (i - j) >= 0 and (i + j) < bar_index
                        if high[i - j] >= high[i] or high[i + j] >= high[i]
                            is_pivot_high := false
                            break
                
                if is_pivot_high
                    total_pivots += 1
                    
                    float vol_at_i = nz(volume[i], 0)
                    float vol_sma_at_i = nz(ta.sma(volume, vol_len)[i], 1)
                    
                    if vol_at_i > vol_sma_at_i * 1.1
                        float vol_ratio = vol_at_i / math.max(1.0, vol_sma_at_i)
                        
                        float base_strength = vol_ratio
                        float confluence_bonus = 0.0
                        
                        if useRSI and i >= rsiPeriod
                            float rsi_val = ta.rsi(close, rsiPeriod)[i]
                            if rsi_val >= rsiOverbought
                                confluence_bonus += 1.0
                            else if rsi_val >= 60
                                confluence_bonus += 0.5
                        
                        if useMACD and i >= macdSlowLength
                            [macdLine, signalLine, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
                            if macdLine[i] > 0 and macdLine[i] < signalLine[i]
                                confluence_bonus += 1.0
                            else if macdLine[i] > 0
                                confluence_bonus += 0.3
                        
                        if detectConfluence and i >= ema200_length
                            float ema200_val = ta.ema(ema200_source, ema200_length)[i]
                            float distance = math.abs(high[i] - ema200_val)
                            float atr_val = ta.atr(dynamicATRPeriod)[i]
                            
                            if distance <= atr_val * 0.5
                                confluence_bonus += 1.5
                            else if distance <= atr_val
                                confluence_bonus += 0.8
                            else if distance <= atr_val * 2
                                confluence_bonus += 0.3
                        
                        // Volume confluence
                        if vol_ratio >= minVolumeRatio * 2
                            confluence_bonus += 1.5
                        else if vol_ratio >= minVolumeRatio
                            confluence_bonus += 1.0
                        else if vol_ratio >= minVolumeRatio * 0.7
                            confluence_bonus += 0.5
                        
                        float strength = base_strength + confluence_bonus
                        
                        if strength > 0
                            array.push(orderblock_strengths, strength)
                            total_orderblocks += 1
        
        // Calculate statistics
        float avg_strength = 0.0
        float total_strength = 0.0
        float min_strength = 999.0
        float max_strength = 0.0
        
        if array.size(orderblock_strengths) > 0
            for j = 0 to array.size(orderblock_strengths) - 1
                float str_val = array.get(orderblock_strengths, j)
                total_strength += str_val
                min_strength := math.min(min_strength, str_val)
                max_strength := math.max(max_strength, str_val)
            avg_strength := total_strength / array.size(orderblock_strengths)
        else
            min_strength := 0.0
        
        if avg_strength > bestAvg
            bestAvg := avg_strength
            bestLB := lb
        
        // Conditionally update table
        if OPT_DEBUG
            bool is_best = lb == bestLB
            color row_bg = is_best ? color.rgb(0,150,0) : color.rgb(40,40,40)
            color txt_color = is_best ? color.black : color.white
            
            table.cell(t, 0, row, str.tostring(lb), 
                      bgcolor=row_bg, text_color=txt_color, text_size=tableTextSize)
            table.cell(t, 1, row, str.tostring(total_pivots), 
                      bgcolor=row_bg, text_color=txt_color, text_size=tableTextSize)
            table.cell(t, 2, row, str.tostring(total_orderblocks), 
                      bgcolor=row_bg, text_color=txt_color, text_size=tableTextSize)
            table.cell(t, 3, row, str.tostring(min_strength, "#.#"), 
                      bgcolor=row_bg, text_color=txt_color, text_size=tableTextSize)
            table.cell(t, 4, row, str.tostring(max_strength, "#.#"), 
                      bgcolor=row_bg, text_color=txt_color, text_size=tableTextSize)
            table.cell(t, 5, row, str.tostring(avg_strength, "#.##"), 
                      bgcolor=row_bg, text_color=txt_color, text_size=tableTextSize)
            table.cell(t, 6, row, str.tostring(total_strength, "#.#"), 
                      bgcolor=row_bg, text_color=txt_color, text_size=tableTextSize)
            row += 1
    
    bestLB

// ─────────────────────────────────────────────────────────────────────
// 4) Run optimization
var int optimLB = lookbackPeriod
var bool optDone = false

if enableLookbackOptimization and not optDone
    if barstate.islastconfirmedhistory
        optimLB := f_findBestLBNew()
        optDone := true

// Value used by the rest of the script
int effectiveLookback = enableLookbackOptimization ? optimLB : lookbackPeriod

// Cache ATR calculation with proper initialization
var float atr = ta.atr(dynamicATRPeriod)
atr := nz(ta.atr(dynamicATRPeriod), atr) // Update with new value or keep previous if NA

f_sum(src, len) =>
    math.sum(src, len)

// --- Enhanced Global Variables ---

// ====================================================================
// LIQUIDITY AND ORDER FLOW GLOBAL VARIABLES (ADD THESE)
// ====================================================================

// Enhanced liquidity tracking
var liquidityLevels = array.new<LiquidityLevel>()

// Liquidity pool variables
var label[] liquidityLabels = array.new_label()
var bool liquidityHighDetected = false
var bool liquidityLowDetected = false
var float liquidityHighLevel = na
var float liquidityLowLevel = na
var int liquidityHighTests = 0
var int liquidityLowTests = 0

// Order flow variables  
var bool orderFlowBullish = false
var bool orderFlowBearish = false
var float orderFlowBuyPercent = 50.0
var float orderFlowSellPercent = 50.0
var float orderFlowDelta = 0.0
var bool orderFlowDivergence = false

// Constants for success criteria
var int SUCCESS_BARS_REQUIRED = 20  // Number of bars an orderblock must remain intact after being touched to be considered successful

// Initialize order blocks array
var order_blocks = array.new<OrderBlock>()
// Track filtered blocks count (needs to be updated in main code, not in functions)
var int filtered_blocks_count = 0
// Arrays to track filtered blocks from functions
var filtered_by_create = array.new<bool>(0)
var filtered_by_update = array.new<bool>(0)

// Efficient volume tracking
var float volSMA = 0.0

// Enhanced volume formatting with string caching for better performance
// Cache for formatted volume strings to avoid redundant calculations
var int MAX_CACHE_SIZE = 20  // Limit cache size to prevent memory issues
var float[] cached_volume_values = array.new_float(0)
var string[] cached_volume_strings = array.new_string(0)

// Initialize cache arrays on first bar to ensure they're properly set up
if barstate.isfirst
    array.clear(cached_volume_values)
    array.clear(cached_volume_strings)

formatVolume(vol) =>
    float vol_val = nz(vol, 0)
    
    // Handle invalid volumes safely
    if vol_val <= 0
        "0"
    else
        string formatted_vol = ""
        int cache_index = -1
        
        // Search cache only if not empty
        if array.size(cached_volume_values) > 0
            for i = 0 to array.size(cached_volume_values) - 1
                // Allow 0.1% tolerance for floating point comparison
                if math.abs(array.get(cached_volume_values, i) - vol_val) < vol_val * 0.001
                    cache_index := i
                    break
        
        // Return cached value if found
        if cache_index >= 0 and cache_index < array.size(cached_volume_strings)
            formatted_vol := array.get(cached_volume_strings, cache_index)
        else
            // Format the volume
            if vol_val >= 1e9
                formatted_vol := str.format("{0}B", str.tostring(math.round(vol_val / 1e9, 2)))
            else if vol_val >= 1e6
                formatted_vol := str.format("{0}M", str.tostring(math.round(vol_val / 1e6, 2)))
            else if vol_val >= 1e3
                formatted_vol := str.format("{0}K", str.tostring(math.round(vol_val / 1e3, 2)))
            else
                formatted_vol := str.tostring(math.round(vol_val))
            
            // Add to cache with size management
            array.push(cached_volume_values, vol_val)
            array.push(cached_volume_strings, formatted_vol)
            
            // Maintain cache size
            if array.size(cached_volume_values) > MAX_CACHE_SIZE
                array.shift(cached_volume_values)
                array.shift(cached_volume_strings)
        
        formatted_vol

// Get buy/sell dominance icon
getBuySellIcon(buy_pct, sell_pct) =>
    string icon = ""
    if buy_pct >= buySellDominanceThreshold
        icon := "🔵" // Blue circle for buy dominance
    else if sell_pct >= buySellDominanceThreshold
        icon := "🔴" // Red circle for sell dominance
    else
        icon := "🟡" // Yellow circle for neutral
    icon

getBuySellColor(buy_pct, sell_pct) =>
    color color = na
    if buy_pct >= buySellDominanceThreshold
        color := color.blue
    else if sell_pct >= buySellDominanceThreshold
        color := color.red
    else
        color := color.yellow
    color

// Create an order block with enhanced features
createOrderBlock(is_support, level, level_alt, total_vol, buy_vol, sell_vol, blockColor) =>
    // Pre-filters - trend filter evaluated on the PIVOT BAR (freeze it)
    float ema200_at_pivot = ta.ema(ema200_source, ema200_length)[effectiveLookback]
    bool trend_filter_pass = not useTrendFilter or
         (is_support and level >= ema200_at_pivot) or
         (not is_support and level <= ema200_at_pivot)

    if trend_filter_pass
        // Confluence values frozen at pivot bar
        float rsi_val = ta.rsi(close, rsiPeriod)[effectiveLookback]
        [macdL, macdS, _] = ta.macd(close, macdFastLength, macdSlowLength, macdSignalLength)
        float macdL_pivot = macdL[effectiveLookback]
        float macdS_pivot = macdS[effectiveLookback]

        bool rsi_conf = useRSI and detectConfluence and 
             (is_support ? rsi_val <= rsiOversold : rsi_val >= rsiOverbought)

        bool macd_conf = useMACD and detectConfluence and 
             (is_support ? (macdL_pivot < 0 and macdL_pivot > macdS_pivot) : 
                         (macdL_pivot > 0 and macdL_pivot < macdS_pivot))

        bool ma_conf = detectConfluence ? f_maConfluence(level, is_support) : false
        bool vol_conf = useVolumeProfile ? f_volumeConfluence(total_vol) : false

        // Check if this zone aligns with liquidity pools
        bool has_liquidity = false
        if enableLiquidityPools
            if is_support and liquidityLowDetected
                has_liquidity := math.abs(level - liquidityLowLevel) < atr * 0.5
            else if not is_support and liquidityHighDetected
                has_liquidity := math.abs(level - liquidityHighLevel) < atr * 0.5

        // Check if this zone has order flow imbalance
        bool has_orderflow = false
        if enableOrderFlow
            has_orderflow := (is_support and orderFlowBullish) or (not is_support and orderFlowBearish)

        // Enhance confluence detection with new factors
        [enhanced_confluence, total_confluence_factors, is_liquidity_conf, is_orderflow_conf] = 
             f_enhancedConfluence(level, is_support, has_liquidity, has_orderflow)

        bool has_conf = enhanced_confluence

        // Calculate strength ONCE and use for all filtering
        float vol_ratio = total_vol / math.max(1.0, ta.sma(volume, 20))
        float base_strength_score = f_calculateZoneStrength(vol_ratio, rsi_conf, macd_conf, ma_conf, vol_conf)
        
        // Apply bonuses to strength score
        float enhanced_strength = base_strength_score
        if has_liquidity and enableLiquidityPools
            enhanced_strength *= liquidityStrengthBonus
        if has_orderflow and enableOrderFlow
            enhanced_strength *= 1.5  // Order flow bonus

        float star_thresh = f_getStarRatingThreshold(min_star_rating)

        // Apply all filters
        bool smart_filter_pass = not useSmartFiltering or vol_ratio >= minVolumeRatio or has_conf
        bool star_filter_pass = enhanced_strength >= star_thresh

        if smart_filter_pass and star_filter_pass
            // Remove old blocks if at capacity
            if array.size(order_blocks) >= analysis_blocks
                while array.size(order_blocks) >= analysis_blocks
                    oldest_ob = array.pop(order_blocks)
                    f_cleanupOrderBlock(oldest_ob)

            // Create visual objects
            upper_level = math.max(level, level_alt)
            lower_level = math.min(level, level_alt)
            left_index = bar_index - effectiveLookback
            mid_level = (upper_level + lower_level) / 2

            [enhanced_fill_color, enhanced_border_color] = f_getEnhancedColors(is_support, enhanced_strength, vol_ratio, has_conf, false, false)
            enhanced_border_width = f_getBorderWidth(vol_ratio, has_conf)

            new_box = box.new(
                 left_index, upper_level,
                 bar_index + extendObs, lower_level,
                 border_color=enhanced_border_color,
                 border_width=enhanced_border_width,
                 bgcolor=enhanced_fill_color,
                 extend=extend.right
                 )

            midlineObj = showMidline ? line.new(
                 left_index, mid_level,
                 bar_index + extendObs, mid_level,
                 color=midlineColor,
                 width=midlineLineWidth,
                 style=line.style_dashed
                 ) : na

            // Create the OrderBlock and add to array
            array.unshift(order_blocks, OrderBlock.new(
                 new_box, upper_level, lower_level, is_support, left_index,
                 bar_index, total_vol, buy_vol, sell_vol,
                 false, false, false,  // is_broken, is_mitigated, is_display_hidden
                 enhanced_strength, vol_ratio, has_conf,
                 0, 0.0,  // retest_count, success_rate
                 true, true,  // strength_calculated, meets_min_rating
                 rsi_conf, macd_conf, ma_conf, vol_conf,
                 midlineObj, na, na, na,  // visual elements
                 false, 0, false  // success tracking
                 ))
                 
// Update order blocks with enhanced features
updateOrderBlocks() =>
    if array.size(order_blocks) == 0
        na
    else
        total_volume = 0.0
        for ob in order_blocks
            total_volume += ob.total_volume

        to_remove = array.new_int(0)
        for i = 0 to array.size(order_blocks) - 1
            ob = array.get(order_blocks, i)

            // ====================================================================
            // BUG FIX: STEP 1 - Check for Removal FIRST
            // ====================================================================
            // This block checks if the OB should be removed based on its state from the PREVIOUS bar.
            // This prevents the block from vanishing on the same bar it breaks.
            
            // Combine all removal conditions into a single block for efficiency.
            should_remove = (ob.is_broken and not showBreakerBlocks) or 
                             (ob.is_mitigated and not showMitigated and not ob.is_display_hidden) or
                             (enableTimeFilter and (bar_index - ob.created_at) > maxBlockAge) or
                             (useSmartFiltering and ob.strength_score < minZoneStrength and not ob.has_confluence) or
                             (not ob.meets_min_rating)

            if should_remove
                array.push(to_remove, i)
                // If it's marked for removal, we don't need to do any more processing or visual updates for it.
                continue

            // ====================================================================
            // STEP 2 - Update Performance & State for blocks that are NOT being removed
            // ====================================================================
            
            // Check for retests and update performance metrics
            price_range = math.abs(ob.upper_level - ob.lower_level)
            is_retesting = (ob.is_support and low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25 and not ob.is_broken) or
                           (not ob.is_support and high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25 and not ob.is_broken)

            if is_retesting
                ob.retest_count += 1
                ob.last_touched_bar := bar_index
                if ob.success_evaluated
                    ob.success_evaluated := false
                    ob.was_successful := false
                
                retest_success = ob.is_support ? (close > ob.upper_level) : (close < ob.lower_level)
                if ob.retest_count > 0
                    current_success = ob.success_rate * (ob.retest_count - 1)
                    new_success = current_success + (retest_success ? 1 : 0)
                    ob.success_rate := new_success / ob.retest_count

            if not ob.success_evaluated and ob.last_touched_bar > 0 and (bar_index - ob.last_touched_bar) >= SUCCESS_BARS_REQUIRED
                ob.was_successful := true
                ob.success_evaluated := true
                if debugMode
                    label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level, "Success: Survived " + str.tostring(SUCCESS_BARS_REQUIRED) + " bars", color=color.rgb(0,255,0), style=label.style_label_down)

            // Update status using the current bar's close for real-time feedback
            if not ob.is_broken
                if (ob.is_support and close < ob.lower_level) or (not ob.is_support and close > ob.upper_level)
                    ob.is_broken := true
                    if ob.last_touched_bar > 0 and not ob.success_evaluated and (bar_index - ob.last_touched_bar) < SUCCESS_BARS_REQUIRED
                        ob.was_successful := false
                        ob.success_evaluated := true
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level, "Failed: Broken after " + str.tostring(bar_index - ob.last_touched_bar) + " bars", color=color.rgb(255,64,64), style=label.style_label_down)
                    else if debugMode
                        label.new(bar_index, ob.is_support ? ob.lower_level : ob.upper_level, "Broken: " + (ob.is_support ? "Support" : "Resistance"), color=color.rgb(255,64,64), style=label.style_label_down)

            if ob.is_broken and not ob.is_mitigated
                mitigation_level = ob.is_support ? ob.upper_level : ob.lower_level
                // Corrected to use real-time `close` instead of `close[1]`
                if (ob.is_support and close > mitigation_level) or (not ob.is_support and close < mitigation_level)
                    ob.is_mitigated := true
                    if ob.last_touched_bar > 0 and not ob.success_evaluated and (bar_index - ob.last_touched_bar) < SUCCESS_BARS_REQUIRED
                        ob.was_successful := false
                        ob.success_evaluated := true
                        if debugMode
                            label.new(bar_index, ob.is_support ? ob.upper_level : ob.lower_level, "Failed: Mitigated after " + str.tostring(bar_index - ob.last_touched_bar) + " bars", color=color.rgb(128,128,128), style=label.style_label_down)
                    else if debugMode
                        label.new(bar_index, ob.is_support ? ob.upper_level : ob.lower_level, "Mitigated: " + (ob.is_support ? "Support" : "Resistance"), color=color.rgb(128,128,128), style=label.style_label_down)

            // ====================================================================
            // STEP 3 - Update Visuals
            // ====================================================================
            // This code only runs for blocks that are visible and not being removed.
            
            blockAge = bar_index - ob.created_at
            int ageBasedTransparency = applyBlockAgeFading ? math.min(60 + math.round(blockAge * 0.5), 90) : 85

            if ob.is_display_hidden
                if not na(ob.top_price_label)
                    label.delete(ob.top_price_label)
                    ob.top_price_label := na
                if not na(ob.bottom_price_label)
                    label.delete(ob.bottom_price_label)
                    ob.bottom_price_label := na
                if not na(ob.confluence_label)
                    label.delete(ob.confluence_label)
                    ob.confluence_label := na
            else
                box.set_right(ob.box, bar_index + extendObs)
                if showMidline and not na(ob.midline)
                    line.set_x2(ob.midline, bar_index + extendObs)

                if showPriceLabels
                    // Update the labels
                    if not na(ob.top_price_label)
                        label.delete(ob.top_price_label)
                        ob.top_price_label := na
                    if not na(ob.bottom_price_label)
                        label.delete(ob.bottom_price_label)
                        ob.bottom_price_label := na

                    mid_level = (ob.upper_level + ob.lower_level) / 2
                    buy_pct = ob.total_volume > 0 ? (ob.buy_volume / ob.total_volume) * 100 : 0
                    sell_pct = ob.total_volume > 0 ? (ob.sell_volume / ob.total_volume) * 100 : 0
                    buySellIcon = getBuySellIcon(buy_pct, sell_pct)
                    statusText = ob.is_broken ? "⚠️ BROKEN" : ob.is_mitigated ? "⚡ MIT" : "✅"
                    strengthRating = showZoneStrength ? f_getColorCodedStarRating(ob.strength_score) : ""
                    retestInfo = ob.retest_count > 0 ? "🔄 Retests: " + str.tostring(ob.retest_count) + " | ✓ " + str.tostring(math.round(ob.success_rate * 100, 0)) + "%" : ""
                    ageInfo = "⏱️ Age: " + str.tostring(blockAge) + " bars"
                    confluence_info = ""
                    if detectConfluence and highlightConfluence and ob.has_confluence
                        confluence_factors = array.new_string(0)
                        if ob.rsi_confluence
                            array.push(confluence_factors, "RSI")
                        if ob.macd_confluence
                            array.push(confluence_factors, "MACD")
                        if ob.ma_confluence
                            array.push(confluence_factors, "MA")
                        if ob.volume_confluence
                            array.push(confluence_factors, "VOL")
                        if array.size(confluence_factors) > 0
                            confluence_info := "🎯 " + array.join(confluence_factors, ",")

                    labelText = ""
                    if displayModeOpt == "Compact"
                        labelText := (ob.is_support ? "SUP " : "RES ") + str.tostring(mid_level, "#.###") + " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" + (showZoneStrength ? " " + strengthRating : "") + (confluence_info != "" ? " | " + confluence_info : "")
                    else
                        labelText := "📍 " + (ob.is_support ? "SUP " : "RES ") + statusText + " | " + str.tostring(mid_level, "#.###") + " | " + str.tostring(math.round(ob.strength_score, 1)) + "pts" + (showZoneStrength ? " | " + strengthRating : "") + "\n" + "📊 VOL: " + formatVolume(ob.total_volume) + " | 💪 " + str.tostring(math.round(total_volume > 0 ? (ob.total_volume / total_volume * 100) : 0, 1)) + "%\n" + buySellIcon + " " + (buy_pct > sell_pct ? "BUY" : "SELL") + " " + str.tostring(math.round(math.max(buy_pct, sell_pct), 1)) + "% | B:" + formatVolume(ob.buy_volume) + " S:" + formatVolume(ob.sell_volume) + (ob.retest_count > 0 ? "\n" + retestInfo : "") + "\n" + ageInfo + (confluence_info != "" ? "\n" + confluence_info : "")

                    if ob.is_support
                        ob.bottom_price_label := label.new(bar_index + extendObs, ob.lower_level, labelText, color=color.new(color.black, 20), style=label.style_label_left, textcolor=color.white, size=orderBlockTextSize, yloc=yloc.price)
                    else
                        ob.top_price_label := label.new(bar_index + extendObs, ob.upper_level, labelText, color=color.new(color.black, 20), style=label.style_label_left, textcolor=color.white, size=orderBlockTextSize, yloc=yloc.price)

                // Update colors and styles
                [updated_fill_color, updated_border_color] = f_getEnhancedColors(ob.is_support, ob.strength_score, ob.total_volume > 0 ? ob.total_volume / ta.sma(volume, 20) : 1.0, ob.has_confluence, ob.is_broken, ob.is_mitigated)
                updated_border_width = f_getBorderWidth(ob.total_volume > 0 ? ob.total_volume / ta.sma(volume, 20) : 1.0, ob.has_confluence)
                pulse_effect = pulseNewBlocks and not ob.is_broken and not ob.is_mitigated ? f_getPulseEffect(ob.created_at, bar_index) : 0
                final_box_color = applyBlockAgeFading ? color.new(updated_fill_color, ageBasedTransparency + pulse_effect) : color.new(updated_fill_color, color.t(updated_fill_color) + pulse_effect)
                border_color = getBuySellColor(ob.total_volume > 0 ? (ob.buy_volume / ob.total_volume) * 100 : 0, ob.total_volume > 0 ? (ob.sell_volume / ob.total_volume) * 100 : 0)
                smart_border_style = ob.is_broken ? line.style_dashed : (blockStyle == "Rounded" ? line.style_solid : (blockStyle == "3D Shadow" ? line.style_dotted : orderBlockBorderStyle))
                
                box.set_bgcolor(ob.box, final_box_color)
                box.set_border_color(ob.box, border_color)
                box.set_border_width(ob.box, updated_border_width)
                box.set_border_style(ob.box, smart_border_style)
                
                if showMidline and not na(ob.midline)
                    [theme_fill, theme_border] = f_getThemeColors(themePreset, ob.is_support)
                    line.set_color(ob.midline, ob.is_support ? theme_fill : theme_border)
                
                array.set(order_blocks, i, ob)

        // Remove ineligible blocks at the end
        if array.size(to_remove) > 0
            for i = array.size(to_remove) - 1 to 0 by 1
                idx = array.get(to_remove, i)
                if idx >= 0 and idx < array.size(order_blocks)
                    ob = array.get(order_blocks, idx)
                    f_cleanupOrderBlock(ob)
                    array.remove(order_blocks, idx)
                    if debugMode
                        label.new(bar_index, close, "Removed Block: " + (ob.is_support ? "Support" : "Resistance"), color=color.orange, style=label.style_label_down)

// ====================================================================
// FUNCTIONS FOR BLOCK SORTING (Using a reliable Insertion Sort)
// This non-recursive method is guaranteed to compile and work correctly.
// ====================================================================

// Helper function to calculate the composite score for a single OrderBlock.
_calculateCompositeScore(OrderBlock ob, int current_bar_index) =>
    float recency_score = math.max(0.0, 1.0 - (current_bar_index - ob.created_at) / 500.0)
    float strength_score = ob.strength_score / 10.0
    int confluence_count = (ob.rsi_confluence ? 1 : 0) + (ob.macd_confluence ? 1 : 0) + (ob.ma_confluence ? 1 : 0) + (ob.volume_confluence ? 1 : 0)
    float confluence_score = confluence_count / 4.0
    recency_score * 0.4 + strength_score * 0.4 + confluence_score * 0.2

// Hide overlapping blocks with enhanced priority logic
hideOverlappingBlocks() =>
    if hideOverlaps and array.size(order_blocks) > 1
        to_remove = array.new_int(0)
        
        for i = array.size(order_blocks) - 1 to 1 by 1
            if not array.includes(to_remove, i)
                ob1 = array.get(order_blocks, i)
                
                for j = i - 1 to 0 by 1
                    if not array.includes(to_remove, j)
                        ob2 = array.get(order_blocks, j)
                        
                        if ob1.lower_level <= ob2.upper_level and ob1.upper_level >= ob2.lower_level
                            bool keep_ob1 = false
                            
                            // Priority Logic
                            if ob2.is_broken and not ob1.is_broken
                                keep_ob1 := true
                            else if ob1.is_broken and not ob2.is_broken
                                keep_ob1 := false
                            else if ob1.strength_score > ob2.strength_score
                                keep_ob1 := true
                            else if ob2.strength_score > ob1.strength_score
                                keep_ob1 := false
                            else if ob1.has_confluence and not ob2.has_confluence
                                keep_ob1 := true
                            else if ob2.has_confluence and not ob1.has_confluence
                                keep_ob1 := false
                            else if ob1.total_volume > ob2.total_volume
                                keep_ob1 := true
                            else
                                keep_ob1 := false
                            
                            if keep_ob1
                                array.push(to_remove, j)
                            else
                                array.push(to_remove, i)
                                // The 'break' statement that was here has been removed.
        
        // Remove the weaker blocks (rest of the function is the same)
        if array.size(to_remove) > 0
            for k = array.size(to_remove) - 1 to 0 by 1
                idx = array.get(to_remove, k)
                if idx >= 0 and idx < array.size(order_blocks)
                    ob = array.get(order_blocks, idx)
                    f_cleanupOrderBlock(ob)
                    array.remove(order_blocks, idx)

// Main function to select top blocks for display.
// This function sorts the blocks using a reliable, non-recursive Insertion Sort.
selectTopBlocksForDisplay() =>
    int num_blocks = array.size(order_blocks)
    if num_blocks <= max_blocks
        // No blocks to hide
        array.new<OrderBlock>()
    else
        // Create a mutable copy for sorting
        array<OrderBlock> display_blocks = array.copy(order_blocks)
        
        // Insertion sort with bounds checking
        for i = 1 to num_blocks - 1
            OrderBlock key_block = array.get(display_blocks, i)
            float key_score = _calculateCompositeScore(key_block, bar_index)
            int j = i - 1
            
            // Safe comparison loop
            while j >= 0
                if j >= array.size(display_blocks)  // Bounds check
                    break
                    
                OrderBlock compare_block = array.get(display_blocks, j)
                float compare_score = _calculateCompositeScore(compare_block, bar_index)
                
                if compare_score < key_score
                    if j+1 < array.size(display_blocks)  // Bounds check
                        array.set(display_blocks, j + 1, compare_block)
                    j := j - 1
                else
                    break
            
            // Safe insertion
            if j+1 < array.size(display_blocks)
                array.set(display_blocks, j + 1, key_block)

        // Return blocks beyond max_blocks to hide
        if max_blocks < array.size(display_blocks)
            array.slice(display_blocks, max_blocks)
        else
            array.new<OrderBlock>()
// ====================================================================
// MAIN LOGIC
// ====================================================================

// Reset filtered blocks tracking arrays each bar
array.clear(filtered_by_create)
array.clear(filtered_by_update)

// Clean up any orphaned visual elements on first bar
if barstate.isfirst
    // Clear any existing boxes and labels that might be left over
    int blocks_count = array.size(order_blocks)
    if blocks_count > 0  // Only process if array has elements
        for i = 0 to blocks_count - 1
            ob = array.get(order_blocks, i)
            f_cleanupOrderBlock(ob)
    // Always clear the array, even if it's empty
    array.clear(order_blocks)

// Calculate ATR once per bar for potential reuse
var float current_atr_value = na
current_atr_value := ta.atr(dynamicATRPeriod)

volSMA := ta.sma(math.abs(upAndDownVolume()), vol_len)

float ema200_val = ta.ema(ema200_source, ema200_length)

if debugMode
    // Extract function calls for consistency
    int debug_regime = f_detectMarketRegime()

    // Enhanced debug info for troubleshooting
    string regime_text = debug_regime == 1 ? "Trending" : debug_regime == 2 ? "Volatile" : "Ranging"

    string debug_info = "Lookback: " + str.tostring(effectiveLookback) +
                 (enableLookbackOptimization ? " (Optimized)" : " (Manual)") +
                 "\nRegime: " + regime_text

    label.new(bar_index, high, debug_info, style=label.style_none, color=color.new(color.white, 0), textcolor=color.white)

// Clear old liquidity labels
if array.size(liquidityLabels) > 0
    for lbl in liquidityLabels
        label.delete(lbl)
    array.clear(liquidityLabels)

// Enhanced liquidity pool detection and management
liquidityHighDetected := false
liquidityLowDetected := false
liquidityHighLevel := na
liquidityLowLevel := na
liquidityHighTests := 0
liquidityLowTests := 0

if enableLiquidityPools
    // Update and manage enhanced liquidity levels
    f_updateLiquidityLevels(liquidityLevels)
    
    // Display liquidity levels based on selected style
    f_displayLiquidityLevels(liquidityLevels)
    
    // Show heatmap legend if using heatmap style
    f_createHeatmapLegend()
    
    // Show quality metrics panel if enabled
    if showQualityMetrics
        f_createQualityMetricsPanel(liquidityLevels)
    
    // Maintain compatibility with existing logic by finding strongest levels
    if array.size(liquidityLevels) > 0
        float strongest_high = na
        float strongest_low = na
        int high_tests = 0
        int low_tests = 0
        
        for i = 0 to array.size(liquidityLevels) - 1
            level = array.get(liquidityLevels, i)
            if not level.is_swept and level.strength >= liquidityMinStrength
                if level.is_high and (na(strongest_high) or level.strength > strongest_high)
                    strongest_high := level.level
                    high_tests := level.tests
                    liquidityHighDetected := true
                else if not level.is_high and (na(strongest_low) or level.strength > strongest_low)
                    strongest_low := level.level
                    low_tests := level.tests
                    liquidityLowDetected := true
        
        liquidityHighLevel := strongest_high
        liquidityLowLevel := strongest_low
        liquidityHighTests := high_tests
        liquidityLowTests := low_tests
    
    // Show liquidity sweeps if enabled
    if showLiquiditySweeps and array.size(liquidityLevels) > 0
        for i = 0 to array.size(liquidityLevels) - 1
            level = array.get(liquidityLevels, i)
            if level.is_swept and bar_index - level.created_bar == 1  // Just swept
                sweep_text = level.is_high ? "🚀 HIGH SWEPT" : "🚀 LOW SWEPT"
                label.new(bar_index, level.level, sweep_text, 
                         style=level.is_high ? label.style_label_down : label.style_label_up, 
                         color=color.new(color.orange, 70), textcolor=color.white, size=size.small)

// Detect order flow imbalance (remove the 'bool', 'float' declarations)
orderFlowBullish := false
orderFlowBearish := false
orderFlowBuyPercent := 50.0
orderFlowSellPercent := 50.0
orderFlowDelta := 0.0
orderFlowDivergence := false

if enableOrderFlow
    [bullish, bearish, buyPct, sellPct, delta, divergence] = f_detectOrderFlowImbalance(orderFlowPeriod, orderFlowThreshold, orderFlowMinVolume)
    orderFlowBullish := bullish
    orderFlowBearish := bearish
    orderFlowBuyPercent := buyPct
    orderFlowSellPercent := sellPct
    orderFlowDelta := delta
    orderFlowDivergence := divergence
    
    // Show delta divergence if enabled
    if showDeltaDivergence and divergence
        label.new(bar_index, high + atr * 0.5, "🔄 DELTA DIV", 
                 style=label.style_label_down, color=color.new(color.orange, 70), 
                 textcolor=color.white, size=size.tiny)

// Calculate support and resistance levels using optimized lookback
[supportLevel, supportLevelAlt, resistanceLevel, resistanceLevelAlt, sup_color, res_color, breakout_res, res_holds, sup_holds, breakout_sup, Vol] = calcSupportResistance(close, effectiveLookback, math.abs(upAndDownVolume()), volSMA, atr)

// In main logic for support blocks:
if not na(supportLevel) and Vol > volSMA * volMultiplier
    buyVol = 0.0
    sellVol = 0.0
    totalVol = 0.0
    for i = 1 to effectiveLookback
        totalVol += volume[i]
        if close[i] > open[i]
            buyVol += volume[i]
        else if close[i] < open[i]
            sellVol += volume[i]
    createOrderBlock(true, supportLevel, supportLevelAlt, totalVol, buyVol, sellVol, sup_color)

// In main logic for resistance blocks:
if not na(resistanceLevel) and Vol > volSMA * volMultiplier
    buyVol = 0.0
    sellVol = 0.0
    totalVol = 0.0
    for i = 1 to effectiveLookback
        totalVol += volume[i]
        if close[i] > open[i]
            buyVol += volume[i]
        else if close[i] < open[i]
            sellVol += volume[i]
    createOrderBlock(false, resistanceLevel, resistanceLevelAlt, totalVol, buyVol, sellVol, res_color)

// First, hide overlapping blocks to reduce visual clutter
hideOverlappingBlocks()

// Then, update all blocks (including hidden ones) for analysis
updateOrderBlocks()

// Finally, select top blocks for display based on our criteria
blocks_to_hide = selectTopBlocksForDisplay()

// Hide visual elements for blocks that exceed the display limit
if array.size(blocks_to_hide) > 0
    for ob in blocks_to_hide
        f_cleanupOrderBlock(ob)
        ob.is_display_hidden := true
        ob.is_mitigated := false

    if debugMode and array.size(blocks_to_hide) > 0
        label.new(bar_index, close, "Hidden for display: " + str.tostring(array.size(blocks_to_hide)) + " blocks",
                 color=color.new(color.blue, 70), style=label.style_label_down, textcolor=color.white)

// Count filtered blocks
int filtered_count = 0
if array.size(filtered_by_create) > 0
    for i = 0 to array.size(filtered_by_create) - 1
        if array.get(filtered_by_create, i)
            filtered_count += 1

if array.size(filtered_by_update) > 0
    for i = 0 to array.size(filtered_by_update) - 1
        if array.get(filtered_by_update, i)
            filtered_count += 1

filtered_blocks_count := filtered_count

// Warning for filtered blocks
if array.size(order_blocks) == 0 and filtered_blocks_count > 0
    if debugMode
        label.new(bar_index, high + atr * 3, "⚠️ All orderblocks filtered out by star rating filter! Try lowering the minimum star rating.",
                  color=color.new(color.red, 20), style=label.style_label_down, textcolor=color.white, size=size.normal)
    else
        if bar_index % 20 == 0
            label.new(bar_index, high + atr * 1.5, "⚠️ Blocks filtered by " + star_selection + " rating",
                      color=color.new(color.black, 70), style=label.style_label_down, textcolor=color.new(color.white, 20), size=size.small)

// ====================================================================
// REVERSAL PATTERN DETECTION
// ====================================================================
bullish_reversal = (high[1] > high) and (close[1] < close) and (open[1] < open) and barstate.isconfirmed
bearish_reversal = (low[1] < low) and (close[1] > close) and (open[1] > open) and barstate.isconfirmed

// Enhanced reversal triangles with better visibility
plotshape(
     series = showReversalTriangles and bullish_reversal ? 1 : na,
     title="Bullish Reversal",
     location=location.abovebar,
     color=bullishReversalColor,
     style=shape.triangledown,
     size=size.tiny
     )

plotshape(
     series = showReversalTriangles and bearish_reversal ? 1 : na,
     title="Bearish Reversal",
     location=location.belowbar,
     color=bearishReversalColor,
     style=shape.triangleup,
     size=size.tiny
     )

// ====================================================================
// DEBUG MODE (Enhanced Debug Information)
// ====================================================================
// Create star rating statistics panel
// Calculate number of rows needed based on min_star_rating (header + ratings from min to 5 + description)
var int table_rows = 7  // Default value for initialization
table_rows := 3 + (6 - min_star_rating)  // Header + ratings from min to 5 + description

if debugMode
    var table starStatsTable = table.new(
         position.bottom_left,
         3, table_rows,
         bgcolor = color.new(color.black, 30),
         border_width = 1,
         border_color = color.new(color.white, 70)
         )

    // Initialize the star stats table
    table.cell(starStatsTable, 0, 0, "⭐ Rating", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
    table.cell(starStatsTable, 1, 0, "Count", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
    table.cell(starStatsTable, 2, 0, "Success", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)

    // Add description of success criteria and analysis information
    table.cell(starStatsTable, 0, table_rows - 1, "Success: Block intact " + str.tostring(SUCCESS_BARS_REQUIRED) + " bars after touch" +
             "\nAnalyzing up to " + str.tostring(analysis_blocks) + " blocks",
             text_color=color.rgb(0,255,128), bgcolor=color.new(color.black, 20), text_size=tableTextSize)

    // Initialize star rating counts and success tracking
    var int[] star_counts = array.new_int(5, 0)
    var int[] star_success = array.new_int(5, 0)
    var int[] star_total = array.new_int(5, 0)

    // Reset counts each bar
    for i = 0 to 4
        array.set(star_counts, i, 0)

    // Count blocks by star rating - include all blocks kept for analysis
    if array.size(order_blocks) > 0
        for ob in order_blocks
            // Include all blocks in the statistics, including those hidden for display
            star_rating = ob.strength_score >= 8.0 ? 5 :
                         ob.strength_score >= 6.0 ? 4 :
                         ob.strength_score >= 4.0 ? 3 :
                         ob.strength_score >= 2.0 ? 2 : 1

            array.set(star_counts, star_rating - 1, array.get(star_counts, star_rating - 1) + 1)

            // Track success/failure for performance stats
            // Include all blocks with success data, regardless of display status
            if ob.was_successful and ob.success_evaluated
                array.set(star_success, star_rating - 1, array.get(star_success, star_rating - 1) + 1)
                array.set(star_total, star_rating - 1, array.get(star_total, star_rating - 1) + 1)
            else if ob.is_broken or (ob.success_evaluated and not ob.was_successful)
                array.set(star_total, star_rating - 1, array.get(star_total, star_rating - 1) + 1)

        // Update star stats table
        // Only show ratings that meet or exceed the user's selected minimum star rating
        int table_row = 1  // Start at row 1 (after header)

        for i = min_star_rating - 1 to 4  // Start from min_star_rating-1 (array index) to 4 (5 stars)
            stars = i == 0 ? "⭐" : i == 1 ? "⭐⭐" : i == 2 ? "⭐⭐⭐" : i == 3 ? "⭐⭐⭐⭐" : "⭐⭐⭐⭐⭐"
            count = array.get(star_counts, i)

            // Calculate success rate
            success_rate = array.get(star_total, i) > 0 ?
                         math.round(array.get(star_success, i) / array.get(star_total, i) * 100, 0) :
                         0

            success_text = array.get(star_total, i) > 0 ?
                         str.tostring(success_rate) + "%" :
                         "N/A"

            // Set cell colors based on star rating
            color text_color = i == 4 ? color.rgb(0,255,0) :
                             i == 3 ? color.rgb(144,238,144) :
                             i == 2 ? color.rgb(255,255,0) :
                             i == 1 ? color.rgb(255,165,0) :
                             color.rgb(255,69,0)

            table.cell(starStatsTable, 0, table_row, stars, text_color=text_color, text_size=tableTextSize)
            table.cell(starStatsTable, 1, table_row, str.tostring(count), text_color=color.white, text_size=tableTextSize)
            table.cell(starStatsTable, 2, table_row, success_text, text_color=success_rate >= 70 ? color.rgb(0,255,0) :
                                                                         success_rate >= 50 ? color.rgb(255,255,0) :
                                                                         color.rgb(255,69,0), text_size=tableTextSize)
            table_row += 1  // Move to next row

if debugMode
    // Improved debug label with better styling
    var label debug_label = label.new(
         bar_index, high + atr * 2,
         "",
         color=color.new(color.black, 20),
         style=label.style_label_down,
         textcolor=color.white,
         size=size.normal
         )

    // Create a more comprehensive debug panel
    // Extract function calls for consistency
    int regime = f_detectMarketRegime()
    float adaptive_box_size = f_getAdaptiveBoxSize() // f_getAdaptiveBoxSize now calculates ATR internally
    // float current_atr = current_atr_value // This might be redundant here if adaptive_box_size is used directly

    // Get market regime for display
    string regime_text = regime == 1 ? "Trending" : regime == 2 ? "Volatile" : "Ranging"

    // Get box size
    float effective_box_size = 0.0
    if useDynamicBox
        if adaptiveBoxSize
            effective_box_size = adaptive_box_size // adaptive_box_size is the actual size
        else
            effective_box_size = atr * dynamicBoxMultiplier // atr is global ta.atr(atr_len)
    else
        effective_box_size = atr * box_width // atr is global ta.atr(atr_len)

    // Count visible and hidden blocks using our dedicated flag
    int visible_blocks = 0
    int hidden_blocks = 0
    for ob in order_blocks
        if ob.is_display_hidden
            hidden_blocks += 1
        else
            visible_blocks += 1

    // Count blocks with success data
    int blocks_with_success = 0
    int blocks_with_failure = 0
    for ob in order_blocks
        if ob.success_evaluated
            if ob.was_successful
                blocks_with_success += 1
            else
                blocks_with_failure += 1

    string debug_text = "🐞 Debug Info v4.0:\n" +
                 "📊 OB Count: " + str.tostring(visible_blocks) + " visible / " + str.tostring(hidden_blocks) + " hidden / " + str.tostring(array.size(order_blocks)) + " total\n" +
                 "🔍 Analysis Blocks: " + str.tostring(analysis_blocks) + " max\n" +
                 "📊 Success Data: " + str.tostring(blocks_with_success) + " success / " + str.tostring(blocks_with_failure) + " failure\n" +
                 "📈 Vol SMA: " + str.tostring(volSMA) + "\n" +
                 "📏 ATR: " + str.tostring(atr) + "\n" +
                 "🔍 Market Regime: " + regime_text + "\n" +
                 "🔍 Lookback: " + str.tostring(effectiveLookback) +
                 (enableLookbackOptimization ? " (Real-time Optimized from " + str.tostring(minLookbackRange) + "-" + str.tostring(maxLookbackRange) +
                  ", Manual: " + str.tostring(lookbackPeriod) + ", Updated: Every Bar)" : " (Manual)") + "\n" +
                 "📏 Effective Box Size: " + str.tostring(math.round(effective_box_size, 2)) + "×ATR\n"

    // Add information about the most recent order block if available
    if array.size(order_blocks) > 0
        OrderBlock latest_ob = array.get(order_blocks, 0)
        debug_text += "📌 Last OB: " + (latest_ob.is_support ? "Support" : "Resistance") +
                     " at " + str.tostring(latest_ob.is_support ? latest_ob.upper_level : latest_ob.lower_level) + "\n" +
                     "⏱️ Created: bar " + str.tostring(latest_ob.created_at) + "\n" +
                     "📊 Volume: " + formatVolume(latest_ob.total_volume) + "\n" +
                     "💪 Strength: " + str.tostring(math.round(latest_ob.strength_score, 1)) + "/10\n" +
                     "🎯 Confluence: " + (latest_ob.has_confluence ? "Yes" : "No") + "\n" +
                     "🔄 Retests: " + str.tostring(latest_ob.retest_count) +
                     (latest_ob.retest_count > 0 ? " (Success: " + str.tostring(math.round(latest_ob.success_rate * 100, 0)) + "%)" : "") + "\n" +
                     "🔄 Status: " + (latest_ob.is_broken ? "Broken" : latest_ob.is_mitigated ? "Mitigated" : "Active") + "\n"

    // Add system settings information
    debug_text += "🔍 Lookback: " + str.tostring(effectiveLookback) +
                 (enableLookbackOptimization ? " (Real-time Opt: " + str.tostring(minLookbackRange) + "-" + str.tostring(maxLookbackRange) +
                  ", Manual: " + str.tostring(lookbackPeriod) + ", Updated: Every Bar)" : " (Manual)") + "\n" +
                 "📏 Box Width: " + str.tostring(useDynamicBox ? (na(current_atr_value) ? atr * dynamicBoxMultiplier : current_atr_value * dynamicBoxMultiplier) : atr * box_width) + "\n" +
                 "🧠 Smart Filter: " + str.tostring(useSmartFiltering) + "\n" +
                 "⏰ Time Filter: " + (enableTimeFilter ? "On (" + str.tostring(maxBlockAge) + " bars)" : "Off") + "\n" +
                 "⭐ Min Star Rating: " + str.tostring(min_star_rating) + " " +
                 (min_star_rating == 5 ? "⭐⭐⭐⭐⭐" :
                  min_star_rating == 4 ? "⭐⭐⭐⭐" :
                  min_star_rating == 3 ? "⭐⭐⭐" :
                  min_star_rating == 2 ? "⭐⭐" : "⭐") + "\n" +
                 "🎯 Confluence: " + str.tostring(detectConfluence)

    label.set_text(debug_label, debug_text)
    label.set_xy(debug_label, bar_index, high + atr * 2)

    // Add a statistics table if we have enough data
    if array.size(order_blocks) >= 3
        var table statsTable = table.new(
             position.bottom_right,
             3, 3,
             bgcolor = color.new(color.black, 30),
             border_width = 1,
             border_color = color.new(color.white, 70)
             )

        // Count blocks by type
        int support_count = 0
        int resistance_count = 0
        int confluence_count = 0
        int broken_count = 0

        for ob in order_blocks
            if ob.is_support
                support_count += 1
            else
                resistance_count += 1

            if ob.has_confluence
                confluence_count += 1

            if ob.is_broken
                broken_count += 1

        // Update table with statistics
        table.cell(statsTable, 0, 0, "📊 Statistics", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
        table.cell(statsTable, 1, 0, "Support", text_color=color.rgb(0,220,128), text_size=tableTextSize)
        table.cell(statsTable, 2, 0, str.tostring(support_count), text_color=color.white, text_size=tableTextSize)

        table.cell(statsTable, 1, 1, "Resistance", text_color=color.rgb(255,100,100), text_size=tableTextSize)
        table.cell(statsTable, 2, 1, str.tostring(resistance_count), text_color=color.white, text_size=tableTextSize)

        table.cell(statsTable, 1, 2, "Confluence", text_color=color.rgb(255,215,0), text_size=tableTextSize)
        table.cell(statsTable, 2, 2, str.tostring(confluence_count), text_color=color.white, text_size=tableTextSize)

// Lookback Optimization Display Table
if enableLookbackOptimization and showOptimizedValue
    var table lookbackOptTable = table.new(
         position.middle_right,
         2, 6,
         bgcolor = color.new(color.black, 30),
         border_width = 1,
         border_color = color.new(color.white, 70)
         )

    // Header
    table.cell(lookbackOptTable, 0, 0, "🔧 Lookback Optimization", text_color=color.rgb(255,215,0), bgcolor=color.new(color.black, 20), text_size=tableTextSize)
    table.cell(lookbackOptTable, 1, 0, "", bgcolor=color.new(color.black, 20))

    // Manual vs Optimized comparison
    table.cell(lookbackOptTable, 0, 1, "Manual Value:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    table.cell(lookbackOptTable, 1, 1, str.tostring(lookbackPeriod), text_color=color.rgb(255,255,255), text_size=tableTextSize)

    // Range being tested
    table.cell(lookbackOptTable, 0, 3, "Test Range:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    table.cell(lookbackOptTable, 1, 3, str.tostring(minLookbackRange) + "-" + str.tostring(maxLookbackRange), text_color=color.white, text_size=tableTextSize)

    // Effective Value
    table.cell(lookbackOptTable, 0, 5, "Effective Value:", text_color=color.rgb(180,180,180), text_size=tableTextSize)
    table.cell(lookbackOptTable, 1, 5, str.tostring(effectiveLookback), text_color=color.white, text_size=tableTextSize)
     
// Star rating filter table
var table starRatingTable = table.new(
     position.top_right,  // Changed to top_left to avoid overlap with lookback table
     1, 4,  // Increased rows to 4 to show dynamic adjustment info
     bgcolor = color.new(color.black, 30),
     border_width = 1,
     border_color = color.new(color.white, 70)
     )

// Get dynamic adjustment info
float base_threshold = min_star_rating == 5 ? 8.0 :
                      min_star_rating == 4 ? 6.0 :
                      min_star_rating == 3 ? 4.0 :
                      min_star_rating == 2 ? 2.0 : 0.0
float actual_threshold = f_getStarRatingThreshold(min_star_rating)
string dynamic_status = ""

if dynamicStarRating and min_star_rating > 1
    float adjustment_pct = (actual_threshold / base_threshold - 1.0) * 100
    string adjustment_dir = adjustment_pct < 0 ? "↓" : adjustment_pct > 0 ? "↑" : "="
    dynamic_status := "Dynamic: " + adjustment_dir + " " + str.tostring(math.abs(math.round(adjustment_pct, 1))) + "%"
else
    dynamic_status := "Dynamic: Off"

if debugMode
    // Update star rating filter table
    table.cell(
         starRatingTable, 0, 0,
         "⭐ Min Star Rating",
         text_color=color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 30)
         )

    table.cell(
         starRatingTable, 0, 1,
         (min_star_rating == 5 ? f_getColorCodedStarRating(8.0) :
         min_star_rating == 4 ? f_getColorCodedStarRating(6.0) :
         min_star_rating == 3 ? f_getColorCodedStarRating(4.0) :
         min_star_rating == 2 ? f_getColorCodedStarRating(2.0) : f_getColorCodedStarRating(1.0)),
         text_color=color.rgb(255,215,0),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 50)
         )

    // Add filtered blocks count
    table.cell(
         starRatingTable, 0, 2,
         "Filtered: " + str.tostring(filtered_blocks_count),
         text_color=color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 40)
         )

    // Add dynamic adjustment info
    table.cell(
         starRatingTable, 0, 3,
         dynamic_status,
         text_color=dynamicStarRating ? color.rgb(0,255,128) : color.rgb(180,180,180),
         text_size=tableTextSize,
         bgcolor=color.new(color.black, 40)
         )

// ====================================================================
// MOVING AVERAGE PLOTTING
// ====================================================================
// Enhanced moving averages with improved visibility
plot(
     show_sma ? ta.sma(sma_source, sma_length) : na,
     "SMA",
     color=sma_color,
     linewidth=2,
     style=plot.style_line
     )

plot(
     show_ema50 ? ta.ema(ema50_source, ema50_length) : na,
     "EMA 50",
     color=ema50_color,
     linewidth=2,
     style=plot.style_line
     )

plot(
     show_ema200 ? ta.ema(ema200_source, ema200_length) : na,
     "EMA 200",
     color=ema200_color,
     linewidth=2,
     style=plot.style_line
     )

// ====================================================================
// BAR COLORING FUNCTIONS
// ====================================================================

// Get bar color theme colors
f_getBarColorTheme() =>
    if barColorTheme == 'Custom'
        [customSupportColor, customResistanceColor, customStrongSupportColor,
         customStrongResistanceColor, customNeutralBarColor, customLiquidityColor]
    else if barColorTheme == 'Monochrome'
        [color.white, color.rgb(180, 180, 180), color.rgb(220, 220, 220),
         color.rgb(100, 100, 100), color.rgb(128, 128, 128), color.rgb(160, 160, 160)]
    else if barColorTheme == 'High Contrast'
        [color.lime, color.fuchsia, color.rgb(0, 255, 0),
         color.rgb(255, 0, 255), color.yellow, color.orange]
    else if barColorTheme == 'Pastel'
        [color.rgb(130, 200, 180), color.rgb(220, 150, 170), color.rgb(100, 180, 160),
         color.rgb(200, 130, 150), color.rgb(200, 200, 200), color.rgb(255, 220, 150)]
    else // Default theme
        [color.rgb(0, 255, 128), color.rgb(255, 100, 100), color.rgb(0, 200, 100),
         color.rgb(255, 50, 50), color.rgb(200, 200, 200), color.rgb(255, 215, 0)]

// Calculate proximity score to nearest S/R level
f_calculateProximityScore() =>
    if not enableBarColoring or not barstate.isconfirmed
        [0.0, false, false, 0.0]
    else
        float currentATR = ta.atr(dynamicATRPeriod)
        float minDistance = 999999.0
        bool nearSupport = false
        bool nearResistance = false
        float levelStrength = 0.0

        // Check proximity to order blocks
        if array.size(orderBlocks) > 0
            for i = 0 to array.size(orderBlocks) - 1
                ob = array.get(orderBlocks, i)
                if not na(ob) and not ob.broken
                    float distanceToTop = math.abs(close - ob.top)
                    float distanceToBottom = math.abs(close - ob.bottom)
                    float minDistanceToBlock = math.min(distanceToTop, distanceToBottom)

                    if minDistanceToBlock < minDistance
                        minDistance := minDistanceToBlock
                        nearSupport := ob.is_support
                        nearResistance := not ob.is_support
                        levelStrength := ob.strength

        // Convert distance to ATR-based score
        float proximityScore = currentATR > 0 ? (proximityThreshold * currentATR - minDistance) / (proximityThreshold * currentATR) : 0.0
        proximityScore := math.max(0.0, math.min(1.0, proximityScore))

        [proximityScore, nearSupport, nearResistance, levelStrength]

// Get proximity-based bar color
f_getProximityBasedColor() =>
    if not enableBarColoring
        color(na)
    else
        [proximityScore, nearSupport, nearResistance, levelStrength] = f_calculateProximityScore()

        if proximityScore <= 0.1
            color(na)
        else
            // Get color theme
            [supportColor, resistanceColor, strongSupportColor, strongResistanceColor, neutralColor, liquidityColor] = f_getBarColorTheme()

            // Determine color based on proximity and level type
            color baseColor = nearSupport ?
                             (levelStrength >= strengthThreshold ? strongSupportColor : supportColor) :
                             nearResistance ?
                             (levelStrength >= strengthThreshold ? strongResistanceColor : resistanceColor) :
                             neutralColor

            // Apply intensity and transparency
            float transparency = math.max(50, 100 - (proximityScore * barColorIntensity * 100))
            color.new(baseColor, math.round(transparency))

// Get strength-based bar color
f_getStrengthBasedColor() =>
    if not enableBarColoring
        color(na)
    else
        float maxStrength = 0.0
        bool isSupport = false

        // Find strongest nearby level
        if array.size(orderBlocks) > 0
            for i = 0 to array.size(orderBlocks) - 1
                ob = array.get(orderBlocks, i)
                if not na(ob) and not ob.broken
                    float distance = math.min(math.abs(close - ob.top), math.abs(close - ob.bottom))
                    float currentATR = ta.atr(dynamicATRPeriod)

                    if distance <= proximityThreshold * currentATR and ob.strength > maxStrength
                        maxStrength := ob.strength
                        isSupport := ob.is_support

        if maxStrength < strengthThreshold
            color(na)
        else
            [supportColor, resistanceColor, strongSupportColor, strongResistanceColor, neutralColor, liquidityColor] = f_getBarColorTheme()
            color baseColor = isSupport ?
                             (maxStrength >= 7.0 ? strongSupportColor : supportColor) :
                             (maxStrength >= 7.0 ? strongResistanceColor : resistanceColor)

            float intensity = math.min(1.0, maxStrength / 10.0) * barColorIntensity
            float transparency = 100 - (intensity * 80)
            color.new(baseColor, math.round(transparency))

// Get liquidity-based bar color
f_getLiquidityBasedColor() =>
    if not enableBarColoring or not enableLiquidityPools
        color(na)
    else
        [supportColor, resistanceColor, strongSupportColor, strongResistanceColor, neutralColor, liquidityColor] = f_getBarColorTheme()

        // Check if near liquidity levels
        bool nearLiquidity = false
        float liquidityStrength = 0.0

        // Simple liquidity detection based on recent highs/lows
        float recentHigh = ta.highest(high, 20)
        float recentLow = ta.lowest(low, 20)
        float currentATR = ta.atr(dynamicATRPeriod)

        if math.abs(close - recentHigh) <= proximityThreshold * currentATR or
           math.abs(close - recentLow) <= proximityThreshold * currentATR
            nearLiquidity := true
            liquidityStrength := 5.0  // Base liquidity strength

        if nearLiquidity
            float transparency = 100 - (barColorIntensity * 60)
            color.new(liquidityColor, math.round(transparency))
        else
            color(na)

// Combined coloring mode
f_getCombinedColor() =>
    if not enableBarColoring
        color(na)
    else
        color proximityColor = f_getProximityBasedColor()
        color strengthColor = f_getStrengthBasedColor()
        color liquidityColor = f_getLiquidityBasedColor()

        // Priority: Liquidity > Strength > Proximity
        if not na(liquidityColor)
            liquidityColor
        else if not na(strengthColor)
            strengthColor
        else
            proximityColor

// Main bar coloring function
f_calculateBarColor() =>
    if not enableBarColoring or not barstate.isconfirmed
        color(na)
    else
        switch barColoringMode
            "Zone Proximity" => f_getProximityBasedColor()
            "Block Strength" => f_getStrengthBasedColor()
            "Liquidity Levels" => f_getLiquidityBasedColor()
            "Combined" => f_getCombinedColor()
            => color(na)

// ====================================================================
// ALERT SELECTION SETTINGS
// ====================================================================
group_alerts = "🚨 Alert Settings"
enableNewBlockAlert = input.bool(false, "🆕 New Block Alert", group=group_alerts, tooltip="Alert when new order block is created")
enableDetailedBlockAlert = input.bool(false, "📝 Detailed Block Alert", group=group_alerts, tooltip="Detailed information about new blocks")
enableHighStrengthAlert = input.bool(false, "💎 High Strength Alert (4+ Stars)", group=group_alerts, tooltip="Alert for high quality blocks only")
enableConfluenceAlert = input.bool(false, "🎯 Confluence Alert", group=group_alerts, tooltip="Alert when confluence zones are created")
enablePremiumAlert = input.bool(false, "👑 Premium Alert (5 Stars + Confluence)", group=group_alerts, tooltip="Alert for highest quality blocks")
enableRetestAlert = input.bool(false, "🔄 Zone Retest Alert", group=group_alerts, tooltip="Alert when price retests a zone")
enableBreakAlert = input.bool(false, "💥 Block Break Alert", group=group_alerts, tooltip="Alert when blocks are broken")
enableMitigationAlert = input.bool(false, "⚡ Mitigation Alert", group=group_alerts, tooltip="Alert when blocks are mitigated")
enableReversalAlert = input.bool(false, "🔄 Reversal Pattern Alert", group=group_alerts, tooltip="Alert for bullish/bearish reversals")
enableRegimeChangeAlert = input.bool(false, "📊 Market Regime Alert", group=group_alerts, tooltip="Alert when market regime changes")
enableVolumeAlert = input.bool(false, "📊 Volume Spike Alert", group=group_alerts, tooltip="Alert for unusual volume")
enableComboAlert = input.bool(false, "🎯 Combo Setup Alert", group=group_alerts, tooltip="Alert for perfect setups (block + reversal)")
enableCriticalAlert = input.bool(false, "🚨 Critical Break Alert", group=group_alerts, tooltip="Alert for important level breaks")
enableQuickAlert = input.bool(false, "📱 Quick Mobile Alert", group=group_alerts, tooltip="Short format alerts for mobile")

// ====================================================================
// ENHANCED ALERT CONDITIONS - COMPREHENSIVE ALERT SYSTEM
// ====================================================================

// ====================================================================
// VARIABLES FOR ALERT TRACKING
// ====================================================================
var bool newBlockCreated = false
var float newBlockLevel = na
var float newBlockStrength = na
var bool newBlockHasConfluence = false
var int newBlockStarRating = 0
var bool newBlockIsSupport = false

var bool blockBroken = false
var float brokenBlockLevel = na
var bool brokenBlockIsSupport = false
var bool blockMitigated = false
var float mitigatedBlockLevel = na
var bool mitigatedBlockIsSupport = false

var bool zoneRetest = false
var float retestBlockLevel = na
var bool retestBlockIsSupport = false
var int retestCount = 0

var bool highStrengthBlock = false
var bool confluenceZoneCreated = false

// ====================================================================
// NEW BLOCK CREATION ALERTS
// ====================================================================

// Reset alert flags
newBlockCreated := false
blockBroken := false
blockMitigated := false
zoneRetest := false
highStrengthBlock := false
confluenceZoneCreated := false

// Check if a new block was just created
if array.size(order_blocks) > 0
    OrderBlock latestBlock = array.get(order_blocks, 0)
    
    // Check if this block was created on the current bar
    if latestBlock.created_at == bar_index
        newBlockCreated := true
        newBlockIsSupport := latestBlock.is_support
        newBlockLevel := latestBlock.is_support ? latestBlock.upper_level : latestBlock.lower_level
        newBlockStrength := latestBlock.strength_score
        newBlockHasConfluence := latestBlock.has_confluence
        
        // Calculate star rating
        newBlockStarRating := latestBlock.strength_score >= 8.0 ? 5 :
                             latestBlock.strength_score >= 6.0 ? 4 :
                             latestBlock.strength_score >= 4.0 ? 3 :
                             latestBlock.strength_score >= 2.0 ? 2 : 1
        
        // Check for high strength block (4+ stars)
        highStrengthBlock := newBlockStarRating >= 4
        
        // Check for confluence zone
        confluenceZoneCreated := latestBlock.has_confluence

// ====================================================================
// BLOCK STATE CHANGE ALERTS
// ====================================================================

// Check for broken or mitigated blocks
if array.size(order_blocks) > 0
    for ob in order_blocks
        // Check for newly broken blocks
        if ob.is_broken and not ob.is_mitigated and ob.last_touched_bar == bar_index
            blockBroken := true
            brokenBlockIsSupport := ob.is_support
            brokenBlockLevel := ob.is_support ? ob.lower_level : ob.upper_level
        
        // Check for newly mitigated blocks
        if ob.is_mitigated and ob.last_touched_bar == bar_index
            blockMitigated := true
            mitigatedBlockIsSupport := ob.is_support
            mitigatedBlockLevel := ob.is_support ? ob.upper_level : ob.lower_level
        
        // Check for zone retests
        if not ob.is_broken and not ob.is_mitigated
            price_range = math.abs(ob.upper_level - ob.lower_level)
            is_retesting = (ob.is_support and low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25) or
                          (not ob.is_support and high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25)
            
            if is_retesting
                zoneRetest := true
                retestBlockIsSupport := ob.is_support
                retestBlockLevel := ob.is_support ? ob.upper_level : ob.lower_level
                retestCount := ob.retest_count + 1
                break

// ====================================================================
// MARKET CONDITION VARIABLES
// ====================================================================
var int previous_regime = na
current_regime_int = f_detectMarketRegime()
regime_changed = not na(previous_regime) and previous_regime != current_regime_int
previous_regime := current_regime_int

volume_spike = volume > ta.sma(volume, 20) * volumeSpikeFactor

// Combo conditions
perfect_setup = newBlockCreated and (bullish_reversal or bearish_reversal)

// Confluence retest
confluence_retest = zoneRetest and array.size(order_blocks) > 0
var bool confluence_retest_confirmed = false
if confluence_retest
    for ob in order_blocks
        price_range = math.abs(ob.upper_level - ob.lower_level)
        is_retesting = (ob.is_support and low <= ob.upper_level and low >= ob.upper_level - price_range * 0.25) or
                      (not ob.is_support and high >= ob.lower_level and high <= ob.lower_level + price_range * 0.25)
        if is_retesting and ob.has_confluence
            confluence_retest_confirmed := true
            break

// Critical level break
var bool critical_break = false
if blockBroken and array.size(order_blocks) > 0
    for ob in order_blocks
        if ob.is_broken and ob.last_touched_bar == bar_index and ob.strength_score >= 6.0
            critical_break := true
            break

//────────────────────────────────────────────────────────────
// ALERT MESSAGE CONSTRUCTION & TRIGGERING
//────────────────────────────────────────────────────────────
generateAlertMessage() =>
    string message = ""
    
    // New Block Creation Alerts
    if enableNewBlockAlert and newBlockCreated
        if newBlockIsSupport
            message := message + "🆕 New Support Block at " + str.tostring(newBlockLevel, "#.####") + " | ⭐" + str.tostring(newBlockStarRating) + "/5 | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
        else
            message := message + "🆕 New Resistance Block at " + str.tostring(newBlockLevel, "#.####") + " | ⭐" + str.tostring(newBlockStarRating) + "/5 | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
    
    // High Strength Block Alerts
    if enableHighStrengthAlert and highStrengthBlock
        if newBlockIsSupport
            message := message + "💎 HIGH QUALITY SUPPORT at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐+ rating | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
        else
            message := message + "💎 HIGH QUALITY RESISTANCE at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐+ rating | Strength: " + str.tostring(math.round(newBlockStrength, 1)) + "/10\n"
    
    // Confluence Zone Alerts
    if enableConfluenceAlert and confluenceZoneCreated
        if newBlockIsSupport
            message := message + "🎯 CONFLUENCE SUPPORT ZONE at " + str.tostring(newBlockLevel, "#.####") + " | Multiple technical factors align!\n"
        else
            message := message + "🎯 CONFLUENCE RESISTANCE ZONE at " + str.tostring(newBlockLevel, "#.####") + " | Multiple technical factors align!\n"
    
    // Premium Quality Block Alerts
    if enablePremiumAlert and newBlockCreated and newBlockStarRating == 5 and newBlockHasConfluence
        if newBlockIsSupport
            message := message + "👑 PREMIUM SUPPORT BLOCK at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐⭐ + CONFLUENCE | Highest Quality Zone!\n"
        else
            message := message + "👑 PREMIUM RESISTANCE BLOCK at " + str.tostring(newBlockLevel, "#.####") + " | ⭐⭐⭐⭐⭐ + CONFLUENCE | Highest Quality Zone!\n"
    
    // Zone Retest Alerts
    if enableRetestAlert and zoneRetest
        if retestBlockIsSupport
            message := message + "🔄 Support Zone Retest at " + str.tostring(retestBlockLevel, "#.####") + " | Retest #" + str.tostring(retestCount) + " | Watch for reaction!\n"
        else
            message := message + "🔄 Resistance Zone Retest at " + str.tostring(retestBlockLevel, "#.####") + " | Retest #" + str.tostring(retestCount) + " | Watch for reaction!\n"
    
    // Block Break Alerts
    if enableBreakAlert and blockBroken
        if brokenBlockIsSupport
            message := message + "💥 SUPPORT BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | Watch for retest as new resistance level\n"
        else
            message := message + "💥 RESISTANCE BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | Watch for retest as new support level\n"
    
    // Block Mitigation Alerts
    if enableMitigationAlert and blockMitigated
        if mitigatedBlockIsSupport
            message := message + "⚡ Support Block MITIGATED at " + str.tostring(mitigatedBlockLevel, "#.####") + " | Full price reversal complete\n"
        else
            message := message + "⚡ Resistance Block MITIGATED at " + str.tostring(mitigatedBlockLevel, "#.####") + " | Full price reversal complete\n"
    
    // Reversal Pattern Alerts
    if enableReversalAlert
        if bullish_reversal
            message := message + "🔼 BULLISH REVERSAL detected at " + str.tostring(close, "#.####") + " | Potential upward move\n"
        if bearish_reversal
            message := message + "🔽 BEARISH REVERSAL detected at " + str.tostring(close, "#.####") + " | Potential downward move\n"
    
    // Market Regime Change Alerts
    if enableRegimeChangeAlert and regime_changed
        regime_text = current_regime_int == 1 ? "TRENDING" : current_regime_int == 2 ? "VOLATILE" : "RANGING"
        message := message + "📊 Market Regime Changed to: " + regime_text + " | Adjust strategy accordingly\n"
    
    // Volume Spike Alerts
    if enableVolumeAlert and volume_spike
        message := message + "📊 VOLUME SPIKE detected | " + str.tostring(math.round(volumeSpikeFactor, 1)) + "x average volume | Watch for new order blocks\n"
    
    // Perfect Setup Alerts (New block + Reversal)
    if enableComboAlert and perfect_setup
        if newBlockIsSupport and bullish_reversal
            message := message + "🎯 PERFECT BULLISH SETUP | New Support + Bullish Reversal at " + str.tostring(newBlockLevel, "#.####") + " | High probability!\n"
        if not newBlockIsSupport and bearish_reversal
            message := message + "🎯 PERFECT BEARISH SETUP | New Resistance + Bearish Reversal at " + str.tostring(newBlockLevel, "#.####") + " | High probability!\n"
    
    // Confluence Retest Alerts
    if enableRetestAlert and confluence_retest_confirmed
        message := message + "🎯 CONFLUENCE ZONE RETEST at " + str.tostring(retestBlockLevel, "#.####") + " | Multiple factors aligned | High probability reaction!\n"
    
    // Critical Level Break Alerts
    if enableCriticalAlert and critical_break
        if brokenBlockIsSupport
            message := message + "🚨 CRITICAL SUPPORT BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | High strength level breached | Significant move expected!\n"
        else
            message := message + "🚨 CRITICAL RESISTANCE BROKEN at " + str.tostring(brokenBlockLevel, "#.####") + " | High strength level breached | Significant move expected!\n"
    
    // Quick Mobile Alerts
    if enableQuickAlert
        if newBlockCreated
            block_type = newBlockIsSupport ? "SUP" : "RES"
            message := message + "📦 " + block_type + " " + str.tostring(newBlockLevel, "#.####") + " ⭐" + str.tostring(newBlockStarRating) + "\n"
        if blockBroken
            broken_type = brokenBlockIsSupport ? "SUP" : "RES"
            message := message + "⚡💥 BREAK " + broken_type + " " + str.tostring(brokenBlockLevel, "#.####") + "\n"
    
    message

alertMessage = generateAlertMessage()
if alertMessage != "" and barstate.isconfirmed
    alert(alertMessage, alert.freq_once_per_bar_close)

// Individual Alert Conditions for specific triggers
alertcondition(enableNewBlockAlert and newBlockCreated and newBlockIsSupport,
          title="New Support Block",
          message="New Support Block created with high quality metrics")

alertcondition(enableNewBlockAlert and newBlockCreated and not newBlockIsSupport,
          title="New Resistance Block", 
          message="New Resistance Block created with high quality metrics")

alertcondition(enableHighStrengthAlert and highStrengthBlock and newBlockIsSupport,
          title="High Quality Support Block",
          message="High Quality Support Block detected with 4+ star rating")

alertcondition(enableHighStrengthAlert and highStrengthBlock and not newBlockIsSupport,
          title="High Quality Resistance Block",
          message="High Quality Resistance Block detected with 4+ star rating")

alertcondition(enableConfluenceAlert and confluenceZoneCreated and newBlockIsSupport,
          title="Confluence Support Zone",
          message="Confluence Support Zone detected with multiple technical factors")

alertcondition(enableConfluenceAlert and confluenceZoneCreated and not newBlockIsSupport,
          title="Confluence Resistance Zone",
          message="Confluence Resistance Zone detected with multiple technical factors")

alertcondition(enablePremiumAlert and newBlockCreated and newBlockStarRating == 5 and newBlockHasConfluence and newBlockIsSupport,
          title="Premium Support Block",
          message="Premium Support Block - 5 stars with confluence factors")

alertcondition(enablePremiumAlert and newBlockCreated and newBlockStarRating == 5 and newBlockHasConfluence and not newBlockIsSupport,
          title="Premium Resistance Block",
          message="Premium Resistance Block - 5 stars with confluence factors")

alertcondition(enableRetestAlert and zoneRetest and retestBlockIsSupport,
          title="Support Zone Retest",
          message="Support Zone being retested - watch for price reaction")

alertcondition(enableRetestAlert and zoneRetest and not retestBlockIsSupport,
          title="Resistance Zone Retest",
          message="Resistance Zone being retested - watch for price reaction")

alertcondition(enableBreakAlert and blockBroken and brokenBlockIsSupport,
          title="Support Block Broken",
          message="Support Block has been broken - potential trend change")

alertcondition(enableBreakAlert and blockBroken and not brokenBlockIsSupport,
          title="Resistance Block Broken",
          message="Resistance Block has been broken - potential trend change")

alertcondition(enableMitigationAlert and blockMitigated and mitigatedBlockIsSupport,
          title="Support Block Mitigated",
          message="Support Block has been fully mitigated")

alertcondition(enableMitigationAlert and blockMitigated and not mitigatedBlockIsSupport,
          title="Resistance Block Mitigated",
          message="Resistance Block has been fully mitigated")

alertcondition(enableReversalAlert and bullish_reversal,
          title="Bullish Reversal Pattern",
          message="Bullish Reversal Pattern detected - potential upward move")

alertcondition(enableReversalAlert and bearish_reversal,
          title="Bearish Reversal Pattern",
          message="Bearish Reversal Pattern detected - potential downward move")

alertcondition(enableRegimeChangeAlert and regime_changed and current_regime_int == 1,
          title="Market Now Trending",
          message="Market regime changed to Trending - adjust strategy")

alertcondition(enableRegimeChangeAlert and regime_changed and current_regime_int == 2,
          title="Market Now Volatile", 
          message="Market regime changed to Volatile - adjust strategy")

alertcondition(enableRegimeChangeAlert and regime_changed and current_regime_int == 0,
          title="Market Now Ranging",
          message="Market regime changed to Ranging - adjust strategy")

alertcondition(enableVolumeAlert and volume_spike,
          title="Volume Spike Detected",
          message="Significant volume spike detected - watch for new order blocks")

alertcondition(enableComboAlert and perfect_setup and newBlockIsSupport and bullish_reversal,
          title="Perfect Bullish Setup",
          message="Perfect Bullish Setup - New Support Block + Bullish Reversal")

alertcondition(enableComboAlert and perfect_setup and not newBlockIsSupport and bearish_reversal,
          title="Perfect Bearish Setup", 
          message="Perfect Bearish Setup - New Resistance Block + Bearish Reversal")

alertcondition(enableRetestAlert and confluence_retest_confirmed,
          title="Confluence Zone Retest",
          message="Confluence Zone Retest - High probability reaction expected")

alertcondition(enableCriticalAlert and critical_break and brokenBlockIsSupport,
          title="Critical Support Break",
          message="Critical Support Level Broken - Significant move expected")

alertcondition(enableCriticalAlert and critical_break and not brokenBlockIsSupport,
          title="Critical Resistance Break",
          message="Critical Resistance Level Broken - Significant move expected")

alertcondition(enableQuickAlert and newBlockCreated,
          title="Quick Block Alert",
          message="Quick notification - New order block created")

alertcondition(enableQuickAlert and blockBroken,
          title="Quick Break Alert",
          message="Quick notification - Order block broken")

// ====================================================================
// BAR COLORING APPLICATION
// ====================================================================

// Apply bar coloring
barColorResult = f_calculateBarColor()
barcolor(barColorResult, title="S/R Bar Coloring")